﻿namespace DevExpress.XtraTreeList.Demos {
    partial class ExcelFiltering {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ExcelFiltering));
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule1 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet1 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet1 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon1 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon2 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon3 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule2 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet2 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet2 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon4 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon5 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon6 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule3 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet3 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet3 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon7 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon8 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon9 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule4 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar1 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            this.bcDiscount = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.textEditForDiscount = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bcMPGCity = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcMPGHighway = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcPrice = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.treeListBand2 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcTrademark = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcName = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcCategory = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcModification = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcBodyStyle = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListBand1 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcOrderID = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.textEditForOrderID = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bcSalesDate = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeListBand3 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcCylinders = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcDoors = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcHorsepower = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcTorque = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcTransmissionSpeeds = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcTransmissionType = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcPhoto = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.textEditForRoots = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemPictureEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditForDiscount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditForOrderID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditForRoots)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPictureEdit1)).BeginInit();
            this.SuspendLayout();
            // 
            // bcDiscount
            // 
            this.bcDiscount.ColumnEdit = this.textEditForDiscount;
            this.bcDiscount.FieldName = "Discount";
            this.bcDiscount.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("bcDiscount.ImageOptions.SvgImage")));
            this.bcDiscount.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.bcDiscount.Name = "bcDiscount";
            this.bcDiscount.Visible = true;
            this.bcDiscount.VisibleIndex = 4;
            // 
            // textEditForDiscount
            // 
            this.textEditForDiscount.AutoHeight = false;
            this.textEditForDiscount.Mask.EditMask = "p";
            this.textEditForDiscount.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.textEditForDiscount.Mask.UseMaskAsDisplayFormat = true;
            this.textEditForDiscount.Name = "textEditForDiscount";
            // 
            // bcMPGCity
            // 
            this.bcMPGCity.Caption = "MPG <b>City";
            this.bcMPGCity.FieldName = "MPGCity";
            this.bcMPGCity.Name = "bcMPGCity";
            this.bcMPGCity.Visible = true;
            this.bcMPGCity.VisibleIndex = 6;
            this.bcMPGCity.Width = 64;
            // 
            // bcMPGHighway
            // 
            this.bcMPGHighway.Caption = "MPG <b>Highway";
            this.bcMPGHighway.FieldName = "MPGHighway";
            this.bcMPGHighway.Name = "bcMPGHighway";
            this.bcMPGHighway.Visible = true;
            this.bcMPGHighway.VisibleIndex = 7;
            this.bcMPGHighway.Width = 64;
            // 
            // bcPrice
            // 
            this.bcPrice.FieldName = "ModelPrice";
            this.bcPrice.Name = "bcPrice";
            this.bcPrice.Visible = true;
            this.bcPrice.VisibleIndex = 3;
            this.bcPrice.Width = 74;
            // 
            // treeList1
            // 
            this.treeList1.Bands.AddRange(new DevExpress.XtraTreeList.Columns.TreeListBand[] {
            this.treeListBand2,
            this.treeListBand1,
            this.treeListBand3});
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.bcName,
            this.bcModification,
            this.bcCategory,
            this.bcPrice,
            this.bcMPGCity,
            this.bcMPGHighway,
            this.bcDoors,
            this.bcCylinders,
            this.bcHorsepower,
            this.bcTorque,
            this.bcTransmissionSpeeds,
            this.bcTransmissionType,
            this.bcPhoto,
            this.bcSalesDate,
            this.bcOrderID,
            this.bcDiscount,
            this.bcBodyStyle,
            this.bcTrademark});
            this.treeList1.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeList1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList1.FixedLineWidth = 1;
            treeListFormatRule1.Column = this.bcDiscount;
            treeListFormatRule1.Name = "Format0";
            formatConditionIconSet1.CategoryName = "Ratings";
            formatConditionIconSetIcon1.PredefinedName = "Stars3_1.png";
            formatConditionIconSetIcon1.Value = new decimal(new int[] {
            67,
            0,
            0,
            0});
            formatConditionIconSetIcon1.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon2.PredefinedName = "Stars3_2.png";
            formatConditionIconSetIcon2.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            formatConditionIconSetIcon2.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon3.PredefinedName = "Stars3_3.png";
            formatConditionIconSetIcon3.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon1);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon2);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon3);
            formatConditionIconSet1.Name = "Stars3";
            formatConditionIconSet1.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleIconSet1.IconSet = formatConditionIconSet1;
            treeListFormatRule1.Rule = formatConditionRuleIconSet1;
            treeListFormatRule2.Column = this.bcMPGCity;
            treeListFormatRule2.Name = "Format1";
            formatConditionIconSet2.CategoryName = "Directional";
            formatConditionIconSetIcon4.PredefinedName = "Arrows3_1.png";
            formatConditionIconSetIcon4.Value = new decimal(new int[] {
            67,
            0,
            0,
            0});
            formatConditionIconSetIcon4.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon5.PredefinedName = "Arrows3_2.png";
            formatConditionIconSetIcon5.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            formatConditionIconSetIcon5.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon6.PredefinedName = "Arrows3_3.png";
            formatConditionIconSetIcon6.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon4);
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon5);
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon6);
            formatConditionIconSet2.Name = "Arrows3Colored";
            formatConditionIconSet2.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleIconSet2.IconSet = formatConditionIconSet2;
            treeListFormatRule2.Rule = formatConditionRuleIconSet2;
            treeListFormatRule3.Column = this.bcMPGHighway;
            treeListFormatRule3.Name = "Format2";
            formatConditionIconSet3.CategoryName = "Directional";
            formatConditionIconSetIcon7.PredefinedName = "Arrows3_1.png";
            formatConditionIconSetIcon7.Value = new decimal(new int[] {
            67,
            0,
            0,
            0});
            formatConditionIconSetIcon7.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon8.PredefinedName = "Arrows3_2.png";
            formatConditionIconSetIcon8.Value = new decimal(new int[] {
            33,
            0,
            0,
            0});
            formatConditionIconSetIcon8.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon9.PredefinedName = "Arrows3_3.png";
            formatConditionIconSetIcon9.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSet3.Icons.Add(formatConditionIconSetIcon7);
            formatConditionIconSet3.Icons.Add(formatConditionIconSetIcon8);
            formatConditionIconSet3.Icons.Add(formatConditionIconSetIcon9);
            formatConditionIconSet3.Name = "Arrows3Colored";
            formatConditionIconSet3.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleIconSet3.IconSet = formatConditionIconSet3;
            treeListFormatRule3.Rule = formatConditionRuleIconSet3;
            treeListFormatRule4.Column = this.bcPrice;
            treeListFormatRule4.Name = "Format3";
            formatConditionRuleDataBar1.AutomaticType = DevExpress.XtraEditors.FormatConditionAutomaticType.ZeroBased;
            formatConditionRuleDataBar1.PredefinedName = "Mint";
            treeListFormatRule4.Rule = formatConditionRuleDataBar1;
            this.treeList1.FormatRules.Add(treeListFormatRule1);
            this.treeList1.FormatRules.Add(treeListFormatRule2);
            this.treeList1.FormatRules.Add(treeListFormatRule3);
            this.treeList1.FormatRules.Add(treeListFormatRule4);
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.Name = "treeList";
            this.treeList1.OptionsBehavior.PopulateServiceColumns = true;
            this.treeList1.OptionsClipboard.ClipboardMode = DevExpress.Export.ClipboardMode.Formatted;
            this.treeList1.OptionsCustomization.CustomizationFormSnapMode = DevExpress.Utils.Controls.SnapMode.OwnerControl;
            this.treeList1.OptionsFilter.FilterEditorAllowCustomExpressions = Utils.DefaultBoolean.True;
            this.treeList1.OptionsView.AllowHtmlDrawHeaders = true;
            this.treeList1.OptionsView.FilterCriteriaDisplayStyle = DevExpress.XtraEditors.FilterCriteriaDisplayStyle.Visual;
            this.treeList1.ParentFieldName = "TrademarkID";
            this.treeList1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.textEditForDiscount,
            this.textEditForOrderID,
            this.textEditForRoots,
            this.repositoryItemPictureEdit1});
            this.treeList1.Size = new System.Drawing.Size(784, 432);
            this.treeList1.TabIndex = 0;
            this.treeList1.FilterPopupExcelData += new DevExpress.XtraTreeList.FilterPopupExcelDataEventHandler(this.TreeList_FilterPopupExcelData);
            // 
            // treeListBand2
            // 
            this.treeListBand2.Caption = "Model";
            this.treeListBand2.Columns.Add(this.bcTrademark);
            this.treeListBand2.Columns.Add(this.bcName);
            this.treeListBand2.Columns.Add(this.bcCategory);
            this.treeListBand2.Columns.Add(this.bcModification);
            this.treeListBand2.Columns.Add(this.bcBodyStyle);
            this.treeListBand2.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Left;
            this.treeListBand2.Name = "treeListBand2";
            this.treeListBand2.Width = 275;
            // 
            // bcTrademark
            // 
            this.bcTrademark.FieldName = "Trademark";
            this.bcTrademark.Name = "bcTrademark";
            this.bcTrademark.OptionsColumn.AllowMove = false;
            this.bcTrademark.OptionsColumn.ShowInCustomizationForm = false;
            this.bcTrademark.Width = 68;
            // 
            // bcName
            // 
            this.bcName.FieldName = "Name";
            this.bcName.FieldNameSort = "Trademark";
            this.bcName.Name = "bcName";
            this.bcName.OptionsColumn.AllowEdit = false;
            this.bcName.OptionsColumn.AllowMove = false;
            this.bcName.OptionsFilter.FilterBySortField = DevExpress.Utils.DefaultBoolean.True;
            this.bcName.Visible = true;
            this.bcName.VisibleIndex = 0;
            this.bcName.Width = 69;
            // 
            // bcCategory
            // 
            this.bcCategory.FieldName = "Category";
            this.bcCategory.Name = "bcCategory";
            this.bcCategory.OptionsColumn.AllowMove = false;
            this.bcCategory.OptionsColumn.ShowInCustomizationForm = false;
            this.bcCategory.Width = 84;
            // 
            // bcModification
            // 
            this.bcModification.FieldName = "Modification";
            this.bcModification.Name = "bcModification";
            this.bcModification.OptionsColumn.AllowMove = false;
            this.bcModification.Visible = true;
            this.bcModification.VisibleIndex = 1;
            this.bcModification.Width = 69;
            // 
            // bcBodyStyle
            // 
            this.bcBodyStyle.FieldName = "BodyStyle";
            this.bcBodyStyle.Name = "bcBodyStyle";
            this.bcBodyStyle.OptionsColumn.AllowMove = false;
            this.bcBodyStyle.Visible = true;
            this.bcBodyStyle.VisibleIndex = 2;
            this.bcBodyStyle.Width = 69;
            // 
            // treeListBand1
            // 
            this.treeListBand1.Caption = "Order Info";
            this.treeListBand1.Columns.Add(this.bcOrderID);
            this.treeListBand1.Columns.Add(this.bcPrice);
            this.treeListBand1.Columns.Add(this.bcDiscount);
            this.treeListBand1.Columns.Add(this.bcSalesDate);
            this.treeListBand1.Name = "treeListBand1";
            this.treeListBand1.Width = 298;
            // 
            // bcOrderID
            // 
            this.bcOrderID.Caption = "ID";
            this.bcOrderID.ColumnEdit = this.textEditForOrderID;
            this.bcOrderID.FieldName = "OrderID";
            this.bcOrderID.Name = "bcOrderID";
            this.bcOrderID.OptionsColumn.AllowEdit = false;
            this.bcOrderID.OptionsColumn.ShowInCustomizationForm = false;
            this.bcOrderID.Width = 74;
            // 
            // textEditForOrderID
            // 
            this.textEditForOrderID.AutoHeight = false;
            this.textEditForOrderID.Mask.EditMask = "d5";
            this.textEditForOrderID.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.textEditForOrderID.Mask.UseMaskAsDisplayFormat = true;
            this.textEditForOrderID.Name = "textEditForOrderID";
            // 
            // bcSalesDate
            // 
            this.bcSalesDate.FieldName = "SalesDate";
            this.bcSalesDate.Name = "bcSalesDate";
            this.bcSalesDate.Visible = true;
            this.bcSalesDate.VisibleIndex = 5;
            // 
            // treeListBand3
            // 
            this.treeListBand3.Caption = "Performance";
            this.treeListBand3.Columns.Add(this.bcMPGCity);
            this.treeListBand3.Columns.Add(this.bcMPGHighway);
            this.treeListBand3.Columns.Add(this.bcCylinders);
            this.treeListBand3.Columns.Add(this.bcDoors);
            this.treeListBand3.Columns.Add(this.bcTransmissionSpeeds);
            this.treeListBand3.Columns.Add(this.bcTransmissionType);
            this.treeListBand3.Columns.Add(this.bcHorsepower);
            this.treeListBand3.Columns.Add(this.bcTorque);
            this.treeListBand3.Columns.Add(this.bcPhoto);
            this.treeListBand3.Name = "treeListBand3";
            this.treeListBand3.Width = 193;
            // 
            // bcCylinders
            // 
            this.bcCylinders.FieldName = "Cylinders";
            this.bcCylinders.Name = "bcCylinders";
            this.bcCylinders.Visible = true;
            this.bcCylinders.VisibleIndex = 8;
            this.bcCylinders.Width = 65;
            // 
            // bcDoors
            // 
            this.bcDoors.FieldName = "Doors";
            this.bcDoors.Name = "bcDoors";
            this.bcDoors.Visible = false;
            this.bcDoors.VisibleIndex = -1;
            // 
            // bcHorsepower
            // 
            this.bcHorsepower.FieldName = "Horsepower";
            this.bcHorsepower.Name = "bcHorsepower";
            this.bcPhoto.Visible = false;
            this.bcPhoto.VisibleIndex = -1;
            this.bcHorsepower.Width = 71;
            // 
            // bcTorque
            // 
            this.bcTorque.FieldName = "Torque";
            this.bcTorque.Name = "bcTorque";
            this.bcPhoto.Visible = false;
            this.bcPhoto.VisibleIndex = -1;
            this.bcTorque.Width = 83;
            // 
            // bcTransmissionSpeeds
            // 
            this.bcTransmissionSpeeds.FieldName = "TransmissionSpeeds";
            this.bcTransmissionSpeeds.Name = "bcTransmissionSpeeds";
            this.bcPhoto.Visible = false;
            this.bcPhoto.VisibleIndex = -1;
            // 
            // bcTransmissionType
            // 
            this.bcTransmissionType.FieldName = "TransmissionType";
            this.bcTransmissionType.Name = "bcTransmissionType";
            this.bcPhoto.Visible = false;
            this.bcPhoto.VisibleIndex = -1;
            // 
            // bcPhoto
            // 
            this.bcPhoto.ColumnEdit = this.repositoryItemPictureEdit1;
            this.bcPhoto.FieldName = "Photo";
            this.bcPhoto.Name = "bcPhoto";
            this.bcPhoto.Visible = false;
            this.bcPhoto.VisibleIndex = -1;
            this.bcPhoto.OptionsColumn.AllowEdit = false;
            this.bcPhoto.OptionsFilter.AllowFilter = false;
            this.bcPhoto.Width = 64;
            // 
            // textEditForRoots
            // 
            this.textEditForRoots.AutoHeight = false;
            this.textEditForRoots.Name = "textEditForRoots";
            // 
            // repositoryItemPictureEdit1
            // 
            this.repositoryItemPictureEdit1.Name = "repositoryItemPictureEdit1";
            this.repositoryItemPictureEdit1.PictureInterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            this.repositoryItemPictureEdit1.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Squeeze;
            // 
            // ExcelFiltering
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.treeList1);
            this.Name = "ExcelFiltering";
            ((System.ComponentModel.ISupportInitialize)(this.textEditForDiscount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditForOrderID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEditForRoots)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPictureEdit1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private TreeList treeList1;
        private Columns.TreeListColumn bcOrderID;
        private Columns.TreeListColumn bcPrice;
        private Columns.TreeListColumn bcDiscount;
        private Columns.TreeListColumn bcSalesDate;
        private Columns.TreeListColumn bcName;
        private Columns.TreeListColumn bcModification;
        private Columns.TreeListColumn bcMPGCity;
        private Columns.TreeListColumn bcMPGHighway;
        private Columns.TreeListColumn bcCylinders;
        private Columns.TreeListColumn bcCategory;
        private Columns.TreeListColumn bcDoors;
        private Columns.TreeListColumn bcHorsepower;
        private Columns.TreeListColumn bcTorque;
        private Columns.TreeListColumn bcTransmissionSpeeds;
        private Columns.TreeListColumn bcTransmissionType;
        private Columns.TreeListColumn bcPhoto;
        private Columns.TreeListColumn bcBodyStyle;
        private Columns.TreeListColumn bcTrademark;
        private Columns.TreeListBand treeListBand2;
        private Columns.TreeListBand treeListBand1;
        private Columns.TreeListBand treeListBand3;
        private XtraEditors.Repository.RepositoryItemTextEdit textEditForRoots;
        private XtraEditors.Repository.RepositoryItemTextEdit textEditForDiscount;
        private XtraEditors.Repository.RepositoryItemTextEdit textEditForOrderID;
        private XtraEditors.Repository.RepositoryItemPictureEdit repositoryItemPictureEdit1;
    }
}
