﻿<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
    <controls>
        <controlentry>
            <control>
                <name>ucScrollAnnotationsOptions</name>
                <windowcaption>Scroll Annotations: Colors and Visibility Customization</windowcaption>
                <description>The OptionsScrollAnnotations property allows you to choose annotation types displayed in the scrollbar. The ScrollAnnotationsStyle event is handled to customize annotation colors and alignment.</description>
                <memberlist>TreeList.ScrollAnnotationsStyle,TreeList.OptionsScrollAnnotations</memberlist>
                <dtimage/>
            </control>
        </controlentry>
        <controlentry>
            <control>
                <name>treeList1</name>
                <windowcaption>Scroll Annotations: UI and Data Customization</windowcaption>
                <description>The CustomScrollAnnotation event allows you to specify custom annotations to be displayed in the scrollbar. This code uses custom annotations to create bookmarks.</description>
                <memberlist>TreeList.CustomScrollAnnotation</memberlist>
                <dtimage/>
            </control>
        </controlentry>
    </controls>
</totalinfo>
