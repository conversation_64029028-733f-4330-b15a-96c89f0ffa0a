﻿namespace DevExpress.XtraTreeList.Demos {
    partial class FormatRules {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule1 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet1 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet1 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon1 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon2 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon3 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon4 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon5 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule2 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom1 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule3 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom2 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule4 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar1 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule5 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue1 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule6 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue2 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule7 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet2 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet2 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon6 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon7 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon8 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule8 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue3 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule9 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue4 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule10 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar2 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule11 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar3 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule12 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleContains formatConditionRuleContains1 = new DevExpress.XtraEditors.FormatConditionRuleContains();
            this.colMarketShare = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colSeptSalesPrev = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colMarchSalesPrev = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colSeptSales = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colMarchSales = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRegion = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.treeListBand1 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.treeListBand2 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.treeListBand3 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            this.SuspendLayout();
            // 
            // colMarketShare
            // 
            this.colMarketShare.Caption = "Market Share";
            this.colMarketShare.FieldName = "MarketShare";
            this.colMarketShare.Format.FormatString = "p0";
            this.colMarketShare.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colMarketShare.Name = "colMarketShare";
            this.colMarketShare.Visible = true;
            this.colMarketShare.VisibleIndex = 5;
            this.colMarketShare.Width = 155;
            // 
            // colSeptSalesPrev
            // 
            this.colSeptSalesPrev.Caption = "September";
            this.colSeptSalesPrev.FieldName = "September From Prior Year";
            this.colSeptSalesPrev.Format.FormatString = "p";
            this.colSeptSalesPrev.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSeptSalesPrev.Name = "colSeptSalesPrev";
            this.colSeptSalesPrev.UnboundExpression = "([SeptemberSales] - [SeptemberSalesPrev]) / [SeptemberSales] ";
            this.colSeptSalesPrev.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.colSeptSalesPrev.Visible = true;
            this.colSeptSalesPrev.VisibleIndex = 4;
            this.colSeptSalesPrev.Width = 155;
            // 
            // colMarchSalesPrev
            // 
            this.colMarchSalesPrev.Caption = "March";
            this.colMarchSalesPrev.FieldName = "March From Prior Year";
            this.colMarchSalesPrev.Format.FormatString = "p";
            this.colMarchSalesPrev.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colMarchSalesPrev.Name = "colMarchSalesPrev";
            this.colMarchSalesPrev.UnboundExpression = "([MarchSales] - [MarchSalesPrev]) / [MarchSales] ";
            this.colMarchSalesPrev.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.colMarchSalesPrev.Visible = true;
            this.colMarchSalesPrev.VisibleIndex = 3;
            this.colMarchSalesPrev.Width = 155;
            // 
            // colSeptSales
            // 
            this.colSeptSales.Caption = "September";
            this.colSeptSales.FieldName = "SeptemberSales";
            this.colSeptSales.Name = "colSeptSales";
            this.colSeptSales.Visible = true;
            this.colSeptSales.VisibleIndex = 2;
            this.colSeptSales.Width = 100;
            // 
            // colMarchSales
            // 
            this.colMarchSales.Caption = "March";
            this.colMarchSales.FieldName = "MarchSales";
            this.colMarchSales.Name = "colMarchSales";
            this.colMarchSales.Visible = true;
            this.colMarchSales.VisibleIndex = 1;
            this.colMarchSales.Width = 100;
            // 
            // colRegion
            // 
            this.colRegion.Caption = " Region";
            this.colRegion.FieldName = "Region";
            this.colRegion.Name = "colRegion";
            this.colRegion.Visible = true;
            this.colRegion.VisibleIndex = 0;
            this.colRegion.Width = 101;
            // 
            // treeList1
            // 
            this.treeList1.Bands.AddRange(new DevExpress.XtraTreeList.Columns.TreeListBand[] {
            this.treeListBand1,
            this.treeListBand2,
            this.treeListBand3});
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colRegion,
            this.colMarchSales,
            this.colSeptSales,
            this.colMarchSalesPrev,
            this.colSeptSalesPrev,
            this.colMarketShare});
            this.treeList1.Dock = System.Windows.Forms.DockStyle.Fill;
            treeListFormatRule1.Column = this.colMarketShare;
            treeListFormatRule1.Name = "Market Share Icon Set";
            formatConditionIconSet1.CategoryName = "Directional";
            formatConditionIconSetIcon1.PredefinedName = "Arrows5_1.png";
            formatConditionIconSetIcon1.Value = new decimal(new int[] {
            80,
            0,
            0,
            0});
            formatConditionIconSetIcon1.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon2.PredefinedName = "Arrows5_2.png";
            formatConditionIconSetIcon2.Value = new decimal(new int[] {
            60,
            0,
            0,
            0});
            formatConditionIconSetIcon2.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon3.PredefinedName = "Arrows5_3.png";
            formatConditionIconSetIcon3.Value = new decimal(new int[] {
            40,
            0,
            0,
            0});
            formatConditionIconSetIcon3.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon4.PredefinedName = "Arrows5_4.png";
            formatConditionIconSetIcon4.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            formatConditionIconSetIcon4.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon5.PredefinedName = "Arrows5_5.png";
            formatConditionIconSetIcon5.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon1);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon2);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon3);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon4);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon5);
            formatConditionIconSet1.Name = "Arrows5Colored";
            formatConditionIconSet1.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleIconSet1.IconSet = formatConditionIconSet1;
            treeListFormatRule1.Rule = formatConditionRuleIconSet1;
            treeListFormatRule2.Column = this.colMarketShare;
            treeListFormatRule2.Name = "Market Share Top";
            formatConditionRuleTopBottom1.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom1.Appearance.ForeColor = System.Drawing.Color.Green;
            formatConditionRuleTopBottom1.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom1.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom1.Rank = new decimal(new int[] {
            20,
            0,
            0,
            0});
            formatConditionRuleTopBottom1.RankType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            treeListFormatRule2.Rule = formatConditionRuleTopBottom1;
            treeListFormatRule3.Column = this.colMarketShare;
            treeListFormatRule3.Name = "Market Share Bottom";
            formatConditionRuleTopBottom2.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom2.Appearance.ForeColor = System.Drawing.Color.Red;
            formatConditionRuleTopBottom2.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom2.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom2.Rank = new decimal(new int[] {
            20,
            0,
            0,
            0});
            formatConditionRuleTopBottom2.RankType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleTopBottom2.TopBottom = DevExpress.XtraEditors.FormatConditionTopBottomType.Bottom;
            treeListFormatRule3.Rule = formatConditionRuleTopBottom2;
            treeListFormatRule4.Column = this.colSeptSalesPrev;
            treeListFormatRule4.Name = "Change Sept DataBar";
            formatConditionRuleDataBar1.Appearance.BackColor = System.Drawing.Color.SpringGreen;
            formatConditionRuleDataBar1.Appearance.BackColor2 = System.Drawing.Color.White;
            formatConditionRuleDataBar1.Appearance.BorderColor = System.Drawing.Color.SeaGreen;
            formatConditionRuleDataBar1.Appearance.Options.UseBackColor = true;
            formatConditionRuleDataBar1.Appearance.Options.UseBorderColor = true;
            formatConditionRuleDataBar1.AppearanceNegative.BackColor = System.Drawing.Color.White;
            formatConditionRuleDataBar1.AppearanceNegative.BackColor2 = System.Drawing.Color.LightCoral;
            formatConditionRuleDataBar1.AppearanceNegative.BorderColor = System.Drawing.Color.RosyBrown;
            formatConditionRuleDataBar1.AppearanceNegative.Options.UseBackColor = true;
            formatConditionRuleDataBar1.AppearanceNegative.Options.UseBorderColor = true;
            formatConditionRuleDataBar1.PredefinedName = null;
            treeListFormatRule4.Rule = formatConditionRuleDataBar1;
            treeListFormatRule5.Column = this.colSeptSalesPrev;
            treeListFormatRule5.Name = "Change Sept Positive";
            formatConditionRuleValue1.Appearance.ForeColor = System.Drawing.Color.Green;
            formatConditionRuleValue1.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.Greater;
            formatConditionRuleValue1.Value1 = 0;
            treeListFormatRule5.Rule = formatConditionRuleValue1;
            treeListFormatRule6.Column = this.colSeptSalesPrev;
            treeListFormatRule6.Name = "Change Sept Negative";
            formatConditionRuleValue2.Appearance.ForeColor = System.Drawing.Color.Red;
            formatConditionRuleValue2.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue2.Condition = DevExpress.XtraEditors.FormatCondition.Less;
            formatConditionRuleValue2.Value1 = 0;
            treeListFormatRule6.Rule = formatConditionRuleValue2;
            treeListFormatRule7.Column = this.colMarchSalesPrev;
            treeListFormatRule7.Name = "Change March IconSet";
            formatConditionIconSet2.CategoryName = "PositiveNegative";
            formatConditionIconSetIcon6.PredefinedName = "Triangles3_3.png";
            formatConditionIconSetIcon6.Value = new decimal(new int[] {
            -1,
            -1,
            -1,
            -2147483648});
            formatConditionIconSetIcon6.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon7.PredefinedName = "Triangles3_2.png";
            formatConditionIconSetIcon7.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon8.PredefinedName = "Triangles3_1.png";
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon6);
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon7);
            formatConditionIconSet2.Icons.Add(formatConditionIconSetIcon8);
            formatConditionIconSet2.Name = "PositiveNegativeTriangles";
            formatConditionIconSet2.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Number;
            formatConditionRuleIconSet2.IconSet = formatConditionIconSet2;
            treeListFormatRule7.Rule = formatConditionRuleIconSet2;
            treeListFormatRule8.Column = this.colMarchSalesPrev;
            treeListFormatRule8.Name = "Change March Positive";
            formatConditionRuleValue3.Appearance.ForeColor = System.Drawing.Color.Green;
            formatConditionRuleValue3.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue3.Condition = DevExpress.XtraEditors.FormatCondition.Greater;
            formatConditionRuleValue3.Value1 = 0;
            treeListFormatRule8.Rule = formatConditionRuleValue3;
            treeListFormatRule9.Column = this.colMarchSalesPrev;
            treeListFormatRule9.Name = "Change March Negative";
            formatConditionRuleValue4.Appearance.ForeColor = System.Drawing.Color.Red;
            formatConditionRuleValue4.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue4.Condition = DevExpress.XtraEditors.FormatCondition.Less;
            formatConditionRuleValue4.Value1 = 0;
            treeListFormatRule9.Rule = formatConditionRuleValue4;
            treeListFormatRule10.Column = this.colSeptSales;
            treeListFormatRule10.Name = "Sales Sept DataBar";
            formatConditionRuleDataBar2.MinimumType = DevExpress.XtraEditors.FormatConditionValueType.Number;
            formatConditionRuleDataBar2.PredefinedName = "Mint Gradient";
            treeListFormatRule10.Rule = formatConditionRuleDataBar2;
            treeListFormatRule11.Column = this.colMarchSales;
            treeListFormatRule11.Name = "Sales March DataBar";
            formatConditionRuleDataBar3.MinimumType = DevExpress.XtraEditors.FormatConditionValueType.Number;
            formatConditionRuleDataBar3.PredefinedName = "Green Gradient";
            treeListFormatRule11.Rule = formatConditionRuleDataBar3;
            treeListFormatRule12.ApplyToRow = true;
            treeListFormatRule12.Column = this.colRegion;
            treeListFormatRule12.Name = "Region Name";
            formatConditionRuleContains1.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleContains1.Appearance.Options.UseFont = true;
            formatConditionRuleContains1.Values = new object[] {
        ((object)("Western Europe")),
        ((object)("Eastern Europe")),
        ((object)("North America")),
        ((object)("South America")),
        ((object)("Asia"))};
            treeListFormatRule12.Rule = formatConditionRuleContains1;
            this.treeList1.FormatRules.Add(treeListFormatRule1);
            this.treeList1.FormatRules.Add(treeListFormatRule2);
            this.treeList1.FormatRules.Add(treeListFormatRule3);
            this.treeList1.FormatRules.Add(treeListFormatRule4);
            this.treeList1.FormatRules.Add(treeListFormatRule5);
            this.treeList1.FormatRules.Add(treeListFormatRule6);
            this.treeList1.FormatRules.Add(treeListFormatRule7);
            this.treeList1.FormatRules.Add(treeListFormatRule8);
            this.treeList1.FormatRules.Add(treeListFormatRule9);
            this.treeList1.FormatRules.Add(treeListFormatRule10);
            this.treeList1.FormatRules.Add(treeListFormatRule11);
            this.treeList1.FormatRules.Add(treeListFormatRule12);
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsBehavior.Editable = false;
            this.treeList1.OptionsMenu.ShowConditionalFormattingItem = true;
            this.treeList1.OptionsView.AllowHtmlDrawHeaders = true;
            this.treeList1.OptionsView.ShowBandsMode = DevExpress.Utils.DefaultBoolean.True;
            this.treeList1.ParentFieldName = "RegionID";
            this.treeList1.Size = new System.Drawing.Size(784, 432);
            this.treeList1.TabIndex = 13;
            this.treeList1.Load += new System.EventHandler(this.treeList1_Load);
            // 
            // treeListBand1
            // 
            this.treeListBand1.Caption = "<b>Sales</b>";
            this.treeListBand1.Columns.Add(this.colRegion);
            this.treeListBand1.Columns.Add(this.colMarchSales);
            this.treeListBand1.Columns.Add(this.colSeptSales);
            this.treeListBand1.Name = "treeListBand1";
            this.treeListBand1.Width = 301;
            // 
            // treeListBand2
            // 
            this.treeListBand2.Caption = "<b>Change From Prior Year</b>";
            this.treeListBand2.Columns.Add(this.colMarchSalesPrev);
            this.treeListBand2.Columns.Add(this.colSeptSalesPrev);
            this.treeListBand2.Name = "treeListBand2";
            this.treeListBand2.Width = 310;
            // 
            // treeListBand3
            // 
            this.treeListBand3.Caption = "<b>Current Market Share </b>";
            this.treeListBand3.Columns.Add(this.colMarketShare);
            this.treeListBand3.Name = "treeListBand3";
            this.treeListBand3.Width = 155;
            // 
            // FormatRules
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.treeList1);
            this.Name = "FormatRules";
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private TreeList treeList1;
        private Columns.TreeListColumn colRegion;
        private Columns.TreeListColumn colMarchSales;
        private Columns.TreeListColumn colSeptSales;
        private Columns.TreeListColumn colMarchSalesPrev;
        private Columns.TreeListColumn colSeptSalesPrev;
        private Columns.TreeListColumn colMarketShare;
        private Columns.TreeListBand treeListBand1;
        private Columns.TreeListBand treeListBand2;
        private Columns.TreeListBand treeListBand3;

    }
}
