<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
  <controls>
    <controlentry>
      <control>
        <name>navigationTreeList</name>
        <windowcaption>Populating with data</windowcaption>
        <description>In this demo, the TreeList is populated with data dynamically via the VirtualTreeGetChildNodes and VirtualTreeGetCellValue events. The VirtualTreeGetChildNodes event is used to supply lists of objects to be represented as nodes. The VirtualTreeGetCellValue event is handled to provide data for node cells.</description>
        <memberlist>TreeList.VirtualTreeGetChildNodes, TreeList.VirtualTreeGetCellValue, Dynamic Data Loading via Events</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>breadCrumbEdit</name>
        <windowcaption>BreadCrumbEdit Options</windowcaption>
        <description>The CustomItemContents event allows you to customize the display text, image and other settings of BreadCrumbEdit items.</description>
        <memberlist>Breadcrumb Behavior</memberlist>
        <dtimage/>
      </control>
    </controlentry>
  </controls>
</totalinfo>
