﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace"></xsd:import>
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0"></xsd:element>
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string"></xsd:attribute>
              <xsd:attribute name="type" type="xsd:string"></xsd:attribute>
              <xsd:attribute name="mimetype" type="xsd:string"></xsd:attribute>
              <xsd:attribute ref="xml:space"></xsd:attribute>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string"></xsd:attribute>
              <xsd:attribute name="name" type="xsd:string"></xsd:attribute>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"></xsd:element>
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"></xsd:element>
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"></xsd:attribute>
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"></xsd:attribute>
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"></xsd:attribute>
              <xsd:attribute ref="xml:space"></xsd:attribute>
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"></xsd:element>
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required"></xsd:attribute>
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"></assembly>
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhyAAgAPf/AM6aAP//nP/7lP/nhP/je//3lP/zjP/vhP//////pf/7jP//tf//1v/bc///9///
        xv/fe//Xc///zv//592sIvDFUffPWv/XhP//7//rhO/ESNalEuO1Lf/Xa//bjP/Xe+fPhP//3v/71v//
        rffnvefLa/fHUv/Ta//PY/e+Sv/flP/7rf/z3vfXY96yKe/DSu/TjN6+Y///vffnc+34/vn5+fr+/8jk
        yeX0/pa/0ODy/c7Wv9Po5/fvzv/jnNfu/Oe+WueyMen3/v/73vffre+2Of/3pf/3rf/z1t6+Wv/35//b
        a//vpd6yOc6eEP/779TEft6qIee2Mf/TY9zw/f/HWv/fc96yMc7p/O/LUsnn+4ynqNamEOfDWv/7zu/f
        re/XWs6eCP/75+++QvT8//39/ffntf/zznd3d2dnZ/f9//ffa5iYmPH6/ufLe1NTU367+sTk+ysrK/X1
        9XNzc/Hx8fz+/962Ke7u7uHh4TY2Nrbd+enq2dPr/N7x5Jm+spa9uZW9xeLy4JS85NLq1r/i+ix7y6PT
        97Hb+azY+OTz6Cl5y97e3tTp+JCQkKSkpCgoKJzC6NTU1NDQ0ERERE9PT4655MrKymNjYzqL3j2L3dju
        /OTk5IiIiNvb2/Ly8jk5OajL67fT70xMTEhISG9vb0GP4IfB/P7//2mt8+rq6lWf6/7+/jGD1dzv/aCg
        oC8vL+Pl5ldXVzIyMqioqJHG/ZycnNXTrGtra5SUlD2J2MXFxb+/vyh6zujo6Obm5oyMjNnZ2drt+tDm
        +N/x/MDb8uv389Ts/C+D2crh9er369Ps/Nzx/Sh5zNPOkuHy/Sx+0i98zDJ+zZrO9i98yzSBz9ru35ar
        o8Dj+ip5yafW+Lrf+p7R95HJ9St7ztjt17vg+sro+yt6ypW925e+tM7nztHpzu/4/szPr+f08eXhwNbc
        udvu9NHWs9Xr8Iiim93bv+LouHuMatzWo+/5+tfXpuLy+dnWsOb0/tTg29TPns/Ijd3Nh9/x+N/dsf//
        /yH/C05FVFNDQVBFMi4wAwEAAAAh+QQJMgD/ACwAAAAAyAAgAAAI/wD/CRxIsKDBgwgTKlzIsKHDhxAj
        SpxIsaLFixgzatzIsaPHjyBDihxJsqTJkyhTqlzJsqXLlzBjypxJsyMwR7nY2Hr1iFbBWjWDCr1I59Oc
        GmVYIVjKZuApBHCGSp2aEIBVq5jS4CpFBw1SBJ3+wVmKIBXVsyGvqp0IAMMECQ8WMHjjKQ8nX3jqHEVD
        dukqtIA3tn0blwEJAATVWkUIIEGAx4/FUCI1qtKbPL9U4UmDCpUdGzZMBR5tsTHkyG7UmjkTgkFcxAUB
        eDn9OIEIPbx2XZokKRijOrJsqCHThkYr0sghyqYdwLaIBzJGMIedWDrzx5DkwJqlB1Ss4cWF4P/Q4apX
        8vMLAVi/zj4A9YEAVgiYT7++AF2WIoWSQ0MIPygAQiFPBOgVeFB89iVYnwIzgMGFWmFEAIARClRo4YWa
        iLLPPbeowwcf/eGDwyBPVGDgifBReOGKDN5RQgwgfEFCXwiUCEABGQyQ4446ZtKIP+fQQ48xxqSjjj39
        DOJABBSgiOKNPEY5wBolIOCAA25NcMGVVzIJgAoEhCkmAS1kMcYWNCijjCJs+gEPO+NgMISJTp745Zhi
        ljlGla0x4CcDF2Ag6Jz/AOCDFQ008IILXcBIAgsI5CDIpH5YY80640wwAZN12nlooos2CsKjCMD1wKmn
        QqDppk1+ecIJUsD/YOUEDMC1wAKBfKMrIYRc42cIdHZaoKuwyuoArbbequytH/xKJwAeoIACEA6MkMC1
        2F4LiDnllANIAxJIwKmww0Y7bbXZppstuOI2WegFVaQAAwPtBUDODX8EwG6w5J4HALzy0lsvZPvC54EJ
        QbAgn4L0/TFfAzKM26+/Bye8MMP1QSwxtCk04UABIIcs8sgRLMDvxMhx7PHILItcMr//FgGCCAbUbPPN
        OHcgMcopXyAzzTgHbbPO7goEwAdR9HDEAUw37fTTHZzM82hHJ73001g3HXVsEVzhwABghy322AMsUfTU
        pAHQ9ddktw222bFdkAQSeNYdZguLOoG2v3LTU203nni7oHdsJhDBBASIJ454AxZowMEG7+2dcuGHK544
        445DrhAACHyQaKJTNP545JKjx7nnn4eeOemMKdGBBaJrXnqnALgO++oSxc767CjqXlFAACH5BAkUAP8A
        LAAAAADIACAAAAj/AP8JHEiwoMGDCBMqXMiwocOHECNKnEixIkVgjnKxsfXqEa2CtSyKHEmypMmTCul8
        mlOjDCsEMNkMPIUADsqbOHPqvIkpDa5SdNC0RNDpHxyYCFLtXMq0qdOCbzzl4eQLTx2WaJDCXPW0q9ev
        IimRGlXpTZ5fqvCkQYXKjg0bpsDKnUv3oB5euy5NkhSMUR1ZNtSQaUOjVd3DiL1CkgNrlh5QsQQTFoJD
        h6teiTNr1qnLUqRQcmhQfkblR59mm1OTBMC6NYC6mkQVSzasMpVNfbBoWaQ6s+vWEwFgmCDhwQIGJF4P
        /K1cZ6ZGwojpKH1Md5xCvHsjFk7cOPLm/5gj/wSQIIB582LcuDZzJgQD4+BxImP2Y5n1Qtv2ZNOemPx5
        9Oq1xp578BkEgBf/mZeACCI8IMMICQYQH07QhBMHNuDsgUgi4vC3HYIJLtjggxFOCACEEab434Q4eaNh
        ItocYoiH26GoooomriDAjjz26KMCM4DBhWthRJCTIYdwMw01NG6no49Q8gikkERGAIARCmSp5ZZA3lFC
        DCB8QYJWCDxRgU7RdCNNk9thySWXM3gJpphkmglAARkMkOeeeg6wRgkIOODAcBNcIKigEVCwUzVsbodn
        n3zm+Wegg05Q6KEOJAqACgR06ikBLWQxBqDuMWAqAxdgoOoQZzbqanCcfv/aaaijIlDqqamueiYAPljR
        QAMvuNAFmCSwgEBxDySbLASWTpDoq9BCxKuvwApLrLHIKvsAs5Y+u+kJJ0gBQ6ATMFDcAuimi+4HpobQ
        arTwLvRtuOM6UO656qbLLgPuCgSAByigAIQDIyRg8MEIH9yABBI8G+/D4wEsMMEJV2zwwg0rGt4FVaQA
        AwM3JojxuxCXvBzHHoMc8nkjL+eBCUGw8GSUPjYgg8Mm5/wvzDLTDKXNOP+bQhMOFGD00UgnHcECJOdc
        stBEJy010kuTDMAFRYAgggFcd+311x3g7PTTWGv99dleh62xvx9E0cMRB8Qt99x0d9D02A8D0PbbdPeA
        PbfdBQEQwRUODGD44YgnPsASa+MNseCEKy754YwHfkESSMiq+azBOuG4zpdnvrmsLXRuoAlEMAHB6qyv
        3oAFGnCwAYufxwsA6qq3zvrrsc+uEAAIfPDrr1PALjvttT8d/PANFN878oEr0YEFxvue/PUASE/98xJV
        D/31Y3tfUUAAIfkECRQA/wAsAAAAAMgAIAAACP8A/wkcSLCgwYMIEypcyFAhMEe52Nh69YhWwVoNM2rc
        yLGjx48gQyqk82lOjTKsEKhkM/AUAjgiY8qcSbOmzYGY0uAqRQfNSQSd/sFRiSDVzaNIkyqV+cZTHk6+
        8NQxiYaoylVLs2rdypUSqVGV3uT5pQpPGlSo7NiwYYqr27dwRerhtevSJEnBGNWRZUMNmTY0WsUdTLjw
        QUhyYM3SAyqWX8BCcOhw1cuw5ctwdVmKFEoOjcjPqPzo0wyz6dNJNYkqlmyYZCqb+mDRsgh1XAC4cwOw
        bTNTI2HEdIg+NjtOodq8M+rO7REAhgkSHixgQGL3wOXWk2tExuzHsuKFtu3/yaa9oXPo0qln/4cdIYAE
        AeLHF+NGt5kzIRhIX19+IbRwcWADzh6IJCJOfwy9J9989eV2X377GQSAFwvGl4AIIjwgwwgVBsAfggl5
        Q2Ai2hxiCIgKTdjhhRlu2OGHAHDY4YwLfojiQYYcws001NyYUIw0BumhhCsIYOSRSCapwAxgcKFbGBH4
        aFA03UgjpXtFJqnlkUs2+WQEABihwJhklrnkHSXEAMIXJFiFwBMVXFlQNXIeFKaZeM6ApppsugknAAVk
        MICghA46wBolIOCAA89NcMGii0ZAQZ2U/hiooYUKiqiijE7gKKQOSAqACgSUaioBLWQxRqL5MeAqAxdg
        /yDrEHFWamtBo55qaqqrItDqq7HOGicAPljRQAMvuNCFmiSwgEB0D0QbLQSeTiDprdgKRKyxyCrLrLPQ
        SvsAtZ5eO+oJJ0gBg6ITMBDdAvDGC+8HroZQa7a3npvuug60+6688dLLgL3aeoACCkA4MEICDDfscMMN
        SCDBtfjmazDCCj+sMcMRTzwpexdUkQIMDAi5YMf3VlwpACGPXLLJ8aF8nQcmBMFCllsm2YAMFKu8Ms02
        45zzkTv3DIAHKTThQAFMN+300xEskLLPdR6d9NJPZ9101CmzXAQIIhgg9thkl91Bz1RXfcHXYZft9thn
        f6ztB1H0cMQBeOet994dTIqd9pUA0G333oTr3TeuEVzhwACMN+744wMsIfffcgKQ+OKQZ8645LhekAQS
        uoZeagvJOkH5yp6DLrqupLtgOq4mEMEEBLTXTnsDFmjAwQY2ni4lALHPbnvtuOvOe4oIfHDssVPkvnvv
        vled/PINNG889Lgq0YEFzh8fvc8AaM/99R11j/33t5oPUkAAIfkECRQA/wAsAAAAAMgAIAAACP8A/wkc
        SLCgwYMIEypcyLChw4cQI0qcSLGixYsYM2rcyLGjx48gQ4ocSbLkRmCOcrGx9eoRrYK1TMqcSXMjnU9z
        apRhhaAnm4GnEMCpSbSo0YWY0uAqRQeNTgSd/sHpiSDV0atYi77xlIeTLzx1cqKh2nNV1rNoR1IiNarS
        mzy/VOFJgwqVHRs2TKXdy1ejHl67Lk2SFIxRHVk21JBpQ6PVXgCQIwPoS9khJDmwZukBFUsxYyE4dLjq
        lVFy5IkAMEyQ8GABAxKTB5qOXbmyLkuRQsmhAfoZlR99mpVWzdo1bIKzEQJIEKB5czFuJJs5E4JBa9q1
        92oSVSzZsNBUNvX/waJlkcblzp9Hjzy9+nWDALykb55AhIgHMkbMD4A9+9lMjQhDjA6/HUNeHIWYV5p8
        89V3X3779fcPAPrtZ2F6EvqHFTLM/LDMgYVss0c2G1F44Yn8wbeCACy26OKLCswABheShRGBhlhBE04c
        2ICzByKJiMMRACu+aGSLMc5YYwQAGKHAk1BGGeMdJcQAwhckkIXAExXgiJU3PyaizSGGdNSklGjOQKWV
        WGrJJQAFZDCAnHTOOcAaJSDggAOqTXDBnntGQIGXWBlyCDfTUOMRnHU2emeee/b5J6CCAqACAZhmSkAL
        WYyRZ3UMhMrABRiUOkSXhGIVTTfSfGSppply/+opAqCKSqqpXQLggxUNNPCCC11YSQILCLD2wLHHQjDB
        soKmmlU1IOnKq6/ACkusscg+oCyzg1p6wglSwKDnBAwUt8C56C7wQaghoOrsuw15C664DpBrbrrnrstA
        uwIB4AEKKADhwAgJFGzwwQY3IIEEzcLrsEL+AiwwwQhXnIDCDA864QVVpAADAyimh7G7D5dcEAAcewxy
        yM2NLJsHJgTBQpFHvtiADA2brHO/MMtMc80t3pyzvyk04UABSCet9NIRLEDyziYTbfTSVCvdNMkoFwGC
        CAZ07fXXYHeQM9RRX6A112Cn7bXYGvf7QRQ9HHHA3HTXbXcHT5P9MABvx4Rt9991431yBFc4MMDhiCeu
        +ABLtK13yQAQbvjilB/e+MkXJIEErJxj2sKvTjy+M8qad8755y6EfrIJRDABweuwv96ABRpwsEGGojsM
        AOuuxw777LXfDjECH/Ta6xS024577lETb3wDyAe//MlKdGBB8sIzrz0A1V8vvUTYT6892eFXFBAAIfkE
        CRQA/wAsAAAAAMgAIAAACP8A/wkcSLCgwYMIEypcyLChw4cQI0qcSLGixYsYM2rcyLGjx48gQ4ocSbKk
        yZMoU6pcybKly5cwY8qcSbOmzZsfgTnKxcbWq0e0CtbCSbSoQzqf5tQowwqBUzYDTyGAAxGA1asAjGpd
        iSkNrlJ00CxF0AkAHKcIUjkEgGGChAcLGJDIOhCr1a14O77xlIeTLzx1lKJB63SVXYQAEgRYvFiMG6xm
        zoRgAJdu3ssXKZEaVelNnl+q8KRBhcqODRumJlc2CMALY8YJRIh4IGPE68WWMeuWCEAPr12XJkkKxqiO
        LBtqyLSh0YpxboEAbN+ePv357usMo0OSA2uWHlCxki//F4JDh6teuFmvEMC+vfv3CmaA4YI1TATs+A8C
        WC9Al6VIochBA3nPUPFDH83EN199EQBghAIQRihhfHeUEAMIX5BAGAJPVJDfh3U9qIkoxSQzTHlUbNJH
        PM68c2GGG3YIQAEZDFDjjTYOsEYJCDjgQFsTXOCjjxFQACKIM2bSiDDE6GDgMVhoEUc7g/gIpJBDFgmA
        CgR06SUBLWQxBo+TMWAmAxdgoOYQHh754ZYEIMPMD8tE6U89+lBZ5plprukhAD5Y0UADL7jQxYUksIDA
        Ww802igEE0RapJtvBtrANfM4cws6O+TDwyCMOvoApJIaueUJJ0gBQ48TMPDWArDG/wrrB2aG0Cal+J2a
        6habCOMODzwEIqustDJgK3QeoIACEA6MkMCz0EYLbQMSSDAprrkmu6wDgJhTTjmASBsttdYa+Q8AF1SR
        AgwMUEcdubdiex266rIbADk3kOPuYvDW5YEJQbDA33sEC9CADNfKO++/AfP3R8HuHZwwAB6k0IQDBWSs
        8cYcR7BAvArrRrHFGHNsssYex4tuESCIYMDLMMcscwcJhyzyBSy7LPPOMNNsLnQfRNHDEQcUbfTRSHcA
        ss2XARD00EhHfbTSBQEQwRUODKD11lx3PcASPzONmdVYe2321mBXfUESSHzp9pctFOqE2POu3fbbb8ft
        wtxVm0tABBMQBC544A1YoAEHG1hHd9N+Az644IUfnrhCACDwwaCDTmE44oovvlvll2OuueSds6ZEBxZs
        PrnnlAJweuqkS6R66aznN3tFAQEAIfkEBRQA/wAsAAAAAMgAIAAACP8A/wkcSLCgwYMIEypcyLChw4cQ
        I0qcSLGixYsYM2rcyLGjx48gQ4ocSbKkyZMoU6pcybKly5cwY8qcSbOmzZs4c+rcCRKAz58AeAptCfTn
        RAAYJkh4sIABiaADi0IdSnUjUqVMnU79JxUhgAQBwoYV4waomTMhGDDdWrUtxa9ix5b9eTbtWoMAvMQN
        m0CEiAcyRuwNwNatYYd5B/f9G3hwYQCCB0uOW/iw5YSQJ2smjHeFgM+gQ4tWMAMMF6BhIlxeXRCAZ9Gw
        QZM2jToCACMKcuveTfpOiRggvpBAQJz4kwqsk3PFzZv3DN/AhRc3XgFAgQwDsGvPPmBNCQQOHCT/nXAh
        fPgIFJSztr69fffv4ceXN48egAoC+PMTaJFlzPe0DATIwAUYFDgEcuqtZp9++fHnHwIACkiggcgB4IMV
        DTTwggtdAEcCCwgs9cCII0IwwYnoJajghRlu2CEIH4ZIIokmopiefSecIAUM4E3AwFILBClkkB8EGAKC
        KlqGo448OuAjkEMKWSQDRwoEgAcooACEAyMk4OWXYH7ZgAQSpJikklhqyWWYbHo5ZpnpcXVBFSnAwMBm
        cb2J5JmGATBnnXfiGZaeUXlgQhAsvBabaA3IYCaffRqKqKKLgtboo1em0IQDBXTq6aegRrDAnpC2lemm
        oKb6qah7+lkECCIYryDrrLTW2sGjpZp6waux1urrrLfGaeUHUfRwxAHIJqvssh2QmitVABBr7LLUKtts
        axFc4cAA3Hbr7bcDLCHss1UBkO224KbLrbitXZAEEgzGi18LGzpBbp/uwisvg/S6YG9rJhDBBAQEF0xw
        AxZowMEGld07FAABD2xwwQgrzLBCACDwQYYZTpHwwg07bKrGHDfgscUht6ZEBxZ8fLHI6gGwcssoS+Ry
        yjBbdnNFAQEAOw==
</value>
  </data>
</root>