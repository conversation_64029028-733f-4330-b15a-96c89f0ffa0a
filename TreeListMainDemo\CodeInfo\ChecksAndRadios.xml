﻿<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
    <controls>
        <controlentry>
            <control>
                <name>treeList</name>
                <windowcaption>Node check style</windowcaption>
                <description>The TreeListOptionsView.RootCheckBoxStyle, TreeListNode.ChildrenCheckBoxStyle and TreeListOptionsView.CheckBoxStyle properties specify whether TreeList nodes should display check boxes, radio buttons, or neither.</description>
                <memberlist>TreeListOptionsView.RootCheckBoxStyle, TreeListNode.ChildrenCheckBoxStyle, TreeListOptionsView.CheckBoxStyle</memberlist>
                <dtimage/>
            </control>
        </controlentry>
        <controlentry>
            <control>
                <name>layoutControl.lgOptions</name>
                <windowcaption>Node check options</windowcaption>
                <description>The following options allow you to enable recursive node checking and specify the display style of node check boxes.</description>
                <memberlist>TreeListOptionsBehavior.AllowRecursiveNodeChecking, TreeListOptionsView.RootCheckBoxStyle, TreeListNode.ChildrenCheckBoxStyle, TreeListOptionsView.CheckBoxStyle</memberlist>
                <dtimage/>
            </control>
        </controlentry>
    </controls>
</totalinfo>
