<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
  <controls>
    <controlentry>
      <control>
        <name>treeList1</name>
        <windowcaption>Anchoring columns</windowcaption>
        <description>In this example, a column is anchored to the left edge at design time. You can anchor other columns via the column header menu, which is implemented via the PopupMenuShowing event.</description>
        <memberlist>TreeListColumn.Fixed, TreeList.PopupMenuShowing</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>spinEdit1</name>
        <windowcaption>Fixed line width</windowcaption>
        <description>Specifies the width of fixed panel dividers.</description>
        <memberlist>TreeList.FixedLineWidth</memberlist>
        <dtimage/>
      </control>
    </controlentry>
  </controls>
</totalinfo>
