<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
  <controls>
    <controlentry>
      <control>
        <name>treeList1</name>
        <windowcaption>Drag-and-drop within Tree List</windowcaption>
        <description>The static DragDropManager.Default object provides events to handle drag-and-drop operations within controls. The following code shows a DragDrop event handler for the Tree List control.</description>
        <memberlist></memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>listBoxControl</name>
        <windowcaption>Drag-and-drop within Listbox</windowcaption>
        <description>The static DragDropManager.Default object provides events to handle drag-and-drop operations within controls. The following code shows a DragDrop event handler for the Listbox control.</description>
        <memberlist></memberlist>
        <dtimage/>
      </control>
    </controlentry>
  </controls>
</totalinfo>
