﻿namespace DevExpress.XtraTreeList.Demos {
    partial class StatisticGDP {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule1 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleContains formatConditionRuleContains1 = new DevExpress.XtraEditors.FormatConditionRuleContains();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule2 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleIconSet formatConditionRuleIconSet1 = new DevExpress.XtraEditors.FormatConditionRuleIconSet();
            DevExpress.XtraEditors.FormatConditionIconSet formatConditionIconSet1 = new DevExpress.XtraEditors.FormatConditionIconSet();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon1 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon2 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraEditors.FormatConditionIconSetIcon formatConditionIconSetIcon3 = new DevExpress.XtraEditors.FormatConditionIconSetIcon();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule3 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue1 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule4 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue2 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule5 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar1 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule6 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar2 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule7 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar3 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule8 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar4 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule9 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar5 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule10 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar6 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule11 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar7 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule12 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar8 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule13 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar9 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule14 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar10 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule15 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar11 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule16 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleDataBar formatConditionRuleDataBar12 = new DevExpress.XtraEditors.FormatConditionRuleDataBar();
            this.tlcCountry = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPGrowth = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemTextEdit4 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.tlcGDPBy2004 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.repositoryItemTextEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.tlcGDPBy2005 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2006 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2007 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2008 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2009 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2010 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2011 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2012 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2013 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2014 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.tlcGDPBy2015 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.bcContinent = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2000_2004 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2005_2009 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2010_2014 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.treeListBand1 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.shapefileDataAdapter1 = new DevExpress.XtraMap.ShapefileDataAdapter();
            this.sidePanel1 = new DevExpress.XtraEditors.SidePanel();
            this.treeList1 = new DevExpress.XtraTreeList.TreeList();
            this.treeListBand2 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2004_2007 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2008_2009 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2010_2011 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2012_2013 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcYearsGDPBy2014_2015 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.bcStatistic = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.sidePanel2 = new DevExpress.XtraEditors.SidePanel();
            this.btBackToWorld = new DevExpress.XtraEditors.SimpleButton();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.cbeSelectedYear = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lbStatisticByYear = new DevExpress.XtraEditors.LabelControl();
            this.mapControl1 = new DevExpress.XtraMap.MapControl();
            this.worldLayer = new DevExpress.XtraMap.VectorItemsLayer();
            this.continentsShapefile = new DevExpress.XtraMap.ShapefileDataAdapter();
            this.allCountriesOnSelectedContinentLayer = new DevExpress.XtraMap.VectorItemsLayer();
            this.continentLayer = new DevExpress.XtraMap.VectorItemsLayer();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit3)).BeginInit();
            this.sidePanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            this.sidePanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbeSelectedYear.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl1)).BeginInit();
            this.SuspendLayout();
            // 
            // tlcCountry
            // 
            this.tlcCountry.Caption = "Country";
            this.tlcCountry.FieldName = "Name";
            this.tlcCountry.Name = "tlcCountry";
            this.tlcCountry.OptionsColumn.AllowEdit = false;
            this.tlcCountry.OptionsColumn.AllowFocus = false;
            this.tlcCountry.Visible = true;
            this.tlcCountry.VisibleIndex = 0;
            // 
            // tlcGDPGrowth
            // 
            this.tlcGDPGrowth.Caption = "GDP Growth";
            this.tlcGDPGrowth.ColumnEdit = this.repositoryItemTextEdit4;
            this.tlcGDPGrowth.FieldName = "GDPGrowth";
            this.tlcGDPGrowth.Name = "tlcGDPGrowth";
            this.tlcGDPGrowth.OptionsColumn.AllowEdit = false;
            this.tlcGDPGrowth.OptionsColumn.AllowFocus = false;
            this.tlcGDPGrowth.OptionsColumn.ShowInCustomizationForm = false;
            this.tlcGDPGrowth.ShowUnboundExpressionMenu = true;
            this.tlcGDPGrowth.UnboundExpression = "([GDPBy2015] - [GDPBy2014]) / [GDPBy2015]";
            this.tlcGDPGrowth.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.tlcGDPGrowth.Visible = true;
            this.tlcGDPGrowth.VisibleIndex = 9;
            // 
            // repositoryItemTextEdit4
            // 
            this.repositoryItemTextEdit4.AutoHeight = false;
            this.repositoryItemTextEdit4.Mask.EditMask = "p";
            this.repositoryItemTextEdit4.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.repositoryItemTextEdit4.Mask.UseMaskAsDisplayFormat = true;
            this.repositoryItemTextEdit4.Name = "repositoryItemTextEdit4";
            // 
            // tlcGDPBy2004
            // 
            this.tlcGDPBy2004.Caption = "2004";
            this.tlcGDPBy2004.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2004.FieldName = "GDPBy2004";
            this.tlcGDPBy2004.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2004.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2004.Name = "tlcGDPBy2004";
            this.tlcGDPBy2004.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2004.OptionsColumn.AllowFocus = false;
            // 
            // repositoryItemTextEdit3
            // 
            this.repositoryItemTextEdit3.AutoHeight = false;
            this.repositoryItemTextEdit3.Mask.EditMask = "n";
            this.repositoryItemTextEdit3.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.repositoryItemTextEdit3.Name = "repositoryItemTextEdit3";
            // 
            // tlcGDPBy2005
            // 
            this.tlcGDPBy2005.Caption = "2005";
            this.tlcGDPBy2005.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2005.FieldName = "GDPBy2005";
            this.tlcGDPBy2005.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2005.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2005.Name = "tlcGDPBy2005";
            this.tlcGDPBy2005.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2005.OptionsColumn.AllowFocus = false;
            // 
            // tlcGDPBy2006
            // 
            this.tlcGDPBy2006.Caption = "2006";
            this.tlcGDPBy2006.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2006.FieldName = "GDPBy2006";
            this.tlcGDPBy2006.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2006.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2006.Name = "tlcGDPBy2006";
            this.tlcGDPBy2006.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2006.OptionsColumn.AllowFocus = false;
            // 
            // tlcGDPBy2007
            // 
            this.tlcGDPBy2007.Caption = "2007";
            this.tlcGDPBy2007.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2007.FieldName = "GDPBy2007";
            this.tlcGDPBy2007.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2007.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2007.Name = "tlcGDPBy2007";
            this.tlcGDPBy2007.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2007.OptionsColumn.AllowFocus = false;
            // 
            // tlcGDPBy2008
            // 
            this.tlcGDPBy2008.Caption = "2008";
            this.tlcGDPBy2008.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2008.FieldName = "GDPBy2008";
            this.tlcGDPBy2008.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2008.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2008.Name = "tlcGDPBy2008";
            this.tlcGDPBy2008.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2008.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2008.Visible = true;
            this.tlcGDPBy2008.VisibleIndex = 1;
            // 
            // tlcGDPBy2009
            // 
            this.tlcGDPBy2009.Caption = "2009";
            this.tlcGDPBy2009.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2009.FieldName = "GDPBy2009";
            this.tlcGDPBy2009.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2009.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2009.Name = "tlcGDPBy2009";
            this.tlcGDPBy2009.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2009.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2009.Visible = true;
            this.tlcGDPBy2009.VisibleIndex = 2;
            // 
            // tlcGDPBy2010
            // 
            this.tlcGDPBy2010.Caption = "2010";
            this.tlcGDPBy2010.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2010.FieldName = "GDPBy2010";
            this.tlcGDPBy2010.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2010.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2010.Name = "tlcGDPBy2010";
            this.tlcGDPBy2010.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2010.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2010.Visible = true;
            this.tlcGDPBy2010.VisibleIndex = 3;
            // 
            // tlcGDPBy2011
            // 
            this.tlcGDPBy2011.Caption = "2011";
            this.tlcGDPBy2011.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2011.FieldName = "GDPBy2011";
            this.tlcGDPBy2011.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2011.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2011.Name = "tlcGDPBy2011";
            this.tlcGDPBy2011.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2011.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2011.Visible = true;
            this.tlcGDPBy2011.VisibleIndex = 4;
            // 
            // tlcGDPBy2012
            // 
            this.tlcGDPBy2012.Caption = "2012";
            this.tlcGDPBy2012.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2012.FieldName = "GDPBy2012";
            this.tlcGDPBy2012.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2012.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2012.Name = "tlcGDPBy2012";
            this.tlcGDPBy2012.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2012.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2012.Visible = true;
            this.tlcGDPBy2012.VisibleIndex = 5;
            // 
            // tlcGDPBy2013
            // 
            this.tlcGDPBy2013.Caption = "2013";
            this.tlcGDPBy2013.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2013.FieldName = "GDPBy2013";
            this.tlcGDPBy2013.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2013.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2013.Name = "tlcGDPBy2013";
            this.tlcGDPBy2013.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2013.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2013.Visible = true;
            this.tlcGDPBy2013.VisibleIndex = 6;
            // 
            // tlcGDPBy2014
            // 
            this.tlcGDPBy2014.Caption = "2014";
            this.tlcGDPBy2014.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2014.FieldName = "GDPBy2014";
            this.tlcGDPBy2014.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2014.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2014.Name = "tlcGDPBy2014";
            this.tlcGDPBy2014.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2014.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2014.Visible = true;
            this.tlcGDPBy2014.VisibleIndex = 7;
            // 
            // tlcGDPBy2015
            // 
            this.tlcGDPBy2015.Caption = "2015";
            this.tlcGDPBy2015.ColumnEdit = this.repositoryItemTextEdit3;
            this.tlcGDPBy2015.FieldName = "GDPBy2015";
            this.tlcGDPBy2015.Format.FormatString = "#,##0,,M";
            this.tlcGDPBy2015.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.tlcGDPBy2015.Name = "tlcGDPBy2015";
            this.tlcGDPBy2015.OptionsColumn.AllowEdit = false;
            this.tlcGDPBy2015.OptionsColumn.AllowFocus = false;
            this.tlcGDPBy2015.Visible = true;
            this.tlcGDPBy2015.VisibleIndex = 8;
            // 
            // bcContinent
            // 
            this.bcContinent.Caption = "Continent";
            this.bcContinent.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Left;
            this.bcContinent.Name = "bcContinent";
            // 
            // bcYearsGDPBy2000_2004
            // 
            this.bcYearsGDPBy2000_2004.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2000_2004.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2000_2004.Caption = "2000 - 2004";
            this.bcYearsGDPBy2000_2004.Name = "bcYearsGDPBy2000_2004";
            // 
            // bcYearsGDPBy2005_2009
            // 
            this.bcYearsGDPBy2005_2009.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2005_2009.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2005_2009.Caption = "2005 - 2009";
            this.bcYearsGDPBy2005_2009.Name = "bcYearsGDPBy2005_2009";
            // 
            // bcYearsGDPBy2010_2014
            // 
            this.bcYearsGDPBy2010_2014.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2010_2014.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2010_2014.Caption = "2010 - 2014";
            this.bcYearsGDPBy2010_2014.Name = "bcYearsGDPBy2010_2014";
            // 
            // treeListBand1
            // 
            this.treeListBand1.Caption = "Statistic";
            this.treeListBand1.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Right;
            this.treeListBand1.Name = "treeListBand1";
            // 
            // sidePanel1
            // 
            this.sidePanel1.Controls.Add(this.treeList1);
            this.sidePanel1.Dock = System.Windows.Forms.DockStyle.Left;
            this.sidePanel1.Location = new System.Drawing.Point(0, 0);
            this.sidePanel1.Name = "sidePanel1";
            this.sidePanel1.Size = new System.Drawing.Size(532, 432);
            this.sidePanel1.TabIndex = 2;
            this.sidePanel1.Text = "sidePanel1";
            // 
            // treeList1
            // 
            this.treeList1.Appearance.Empty.BackColor = System.Drawing.Color.Transparent;
            this.treeList1.Appearance.Empty.Options.UseBackColor = true;
            this.treeList1.Appearance.Row.BackColor = System.Drawing.Color.Transparent;
            this.treeList1.Appearance.Row.Options.UseBackColor = true;
            this.treeList1.Appearance.TreeLine.BackColor = System.Drawing.Color.Transparent;
            this.treeList1.Appearance.TreeLine.Options.UseBackColor = true;
            this.treeList1.Bands.AddRange(new DevExpress.XtraTreeList.Columns.TreeListBand[] {
            this.treeListBand2,
            this.bcYearsGDPBy2004_2007,
            this.bcYearsGDPBy2008_2009,
            this.bcYearsGDPBy2010_2011,
            this.bcYearsGDPBy2012_2013,
            this.bcYearsGDPBy2014_2015,
            this.bcStatistic});
            this.treeList1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.tlcCountry,
            this.tlcGDPBy2004,
            this.tlcGDPBy2005,
            this.tlcGDPBy2006,
            this.tlcGDPBy2007,
            this.tlcGDPBy2008,
            this.tlcGDPBy2009,
            this.tlcGDPBy2010,
            this.tlcGDPBy2011,
            this.tlcGDPBy2012,
            this.tlcGDPBy2013,
            this.tlcGDPBy2014,
            this.tlcGDPBy2015,
            this.tlcGDPGrowth});
            this.treeList1.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeList1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList1.FixedLineWidth = 1;
            treeListFormatRule1.ApplyToRow = true;
            treeListFormatRule1.Column = this.tlcCountry;
            treeListFormatRule1.Description = null;
            treeListFormatRule1.Enabled = false;
            treeListFormatRule1.Name = "Region Name";
            formatConditionRuleContains1.PredefinedName = "Bold Text";
            formatConditionRuleContains1.Values = new object[] {
        ((object)("Western Europe")),
        ((object)("Eastern Europe")),
        ((object)("North America")),
        ((object)("South America")),
        ((object)("Asia"))};
            treeListFormatRule1.Rule = formatConditionRuleContains1;
            treeListFormatRule1.StopIfTrue = true;
            treeListFormatRule2.Column = this.tlcGDPGrowth;
            treeListFormatRule2.Description = null;
            treeListFormatRule2.Name = "GDP By Year";
            formatConditionIconSet1.CategoryName = "Directional";
            formatConditionIconSetIcon1.PredefinedName = "Triangles3_1.png";
            formatConditionIconSetIcon1.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon2.PredefinedName = "Triangles3_2.png";
            formatConditionIconSetIcon2.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            formatConditionIconSetIcon2.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSetIcon3.PredefinedName = "Triangles3_3.png";
            formatConditionIconSetIcon3.Value = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            formatConditionIconSetIcon3.ValueComparison = DevExpress.XtraEditors.FormatConditionComparisonType.GreaterOrEqual;
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon1);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon2);
            formatConditionIconSet1.Icons.Add(formatConditionIconSetIcon3);
            formatConditionIconSet1.Name = "Triangles3";
            formatConditionIconSet1.ValueType = DevExpress.XtraEditors.FormatConditionValueType.Number;
            formatConditionRuleIconSet1.IconSet = formatConditionIconSet1;
            treeListFormatRule2.Rule = formatConditionRuleIconSet1;
            treeListFormatRule3.Column = this.tlcGDPGrowth;
            treeListFormatRule3.Description = null;
            treeListFormatRule3.Name = "GDP By Year Positive";
            formatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.GreaterOrEqual;
            formatConditionRuleValue1.PredefinedName = "Green Text";
            formatConditionRuleValue1.Value1 = 0D;
            treeListFormatRule3.Rule = formatConditionRuleValue1;
            treeListFormatRule4.Column = this.tlcGDPGrowth;
            treeListFormatRule4.Description = null;
            treeListFormatRule4.Name = "GDP By Year Negative";
            formatConditionRuleValue2.Condition = DevExpress.XtraEditors.FormatCondition.Less;
            formatConditionRuleValue2.PredefinedName = "Red Text";
            formatConditionRuleValue2.Value1 = 0D;
            treeListFormatRule4.Rule = formatConditionRuleValue2;
            treeListFormatRule5.Column = this.tlcGDPBy2004;
            treeListFormatRule5.Description = null;
            treeListFormatRule5.Enabled = false;
            treeListFormatRule5.Name = "Year 2004";
            formatConditionRuleDataBar1.AllowNegativeAxis = false;
            formatConditionRuleDataBar1.PredefinedName = "Orange Gradient";
            treeListFormatRule5.Rule = formatConditionRuleDataBar1;
            treeListFormatRule6.Column = this.tlcGDPBy2005;
            treeListFormatRule6.Description = null;
            treeListFormatRule6.Enabled = false;
            treeListFormatRule6.Name = "Year 2005";
            formatConditionRuleDataBar2.AllowNegativeAxis = false;
            formatConditionRuleDataBar2.PredefinedName = "Green Gradient";
            treeListFormatRule6.Rule = formatConditionRuleDataBar2;
            treeListFormatRule7.Column = this.tlcGDPBy2006;
            treeListFormatRule7.Description = null;
            treeListFormatRule7.Enabled = false;
            treeListFormatRule7.Name = "Year 2006";
            formatConditionRuleDataBar3.AllowNegativeAxis = false;
            formatConditionRuleDataBar3.PredefinedName = "Blue Gradient";
            treeListFormatRule7.Rule = formatConditionRuleDataBar3;
            treeListFormatRule8.Column = this.tlcGDPBy2007;
            treeListFormatRule8.Description = null;
            treeListFormatRule8.Enabled = false;
            treeListFormatRule8.Name = "Year 2007";
            formatConditionRuleDataBar4.AllowNegativeAxis = false;
            formatConditionRuleDataBar4.PredefinedName = "Violet Gradient";
            treeListFormatRule8.Rule = formatConditionRuleDataBar4;
            treeListFormatRule9.Column = this.tlcGDPBy2008;
            treeListFormatRule9.Description = null;
            treeListFormatRule9.Enabled = false;
            treeListFormatRule9.Name = "Year 2008";
            formatConditionRuleDataBar5.AllowNegativeAxis = false;
            formatConditionRuleDataBar5.PredefinedName = "Raspberry Gradient";
            treeListFormatRule9.Rule = formatConditionRuleDataBar5;
            treeListFormatRule10.Column = this.tlcGDPBy2009;
            treeListFormatRule10.Description = null;
            treeListFormatRule10.Enabled = false;
            treeListFormatRule10.Name = "Year 2009";
            formatConditionRuleDataBar6.AllowNegativeAxis = false;
            formatConditionRuleDataBar6.PredefinedName = "Orange Gradient";
            treeListFormatRule10.Rule = formatConditionRuleDataBar6;
            treeListFormatRule11.Column = this.tlcGDPBy2010;
            treeListFormatRule11.Description = null;
            treeListFormatRule11.Enabled = false;
            treeListFormatRule11.Name = "Year 2010";
            formatConditionRuleDataBar7.AllowNegativeAxis = false;
            formatConditionRuleDataBar7.PredefinedName = "Green Gradient";
            treeListFormatRule11.Rule = formatConditionRuleDataBar7;
            treeListFormatRule12.Column = this.tlcGDPBy2011;
            treeListFormatRule12.Description = null;
            treeListFormatRule12.Enabled = false;
            treeListFormatRule12.Name = "Year 2011";
            formatConditionRuleDataBar8.AllowNegativeAxis = false;
            formatConditionRuleDataBar8.PredefinedName = "Blue Gradient";
            treeListFormatRule12.Rule = formatConditionRuleDataBar8;
            treeListFormatRule13.Column = this.tlcGDPBy2012;
            treeListFormatRule13.Description = null;
            treeListFormatRule13.Enabled = false;
            treeListFormatRule13.Name = "Year 2012";
            formatConditionRuleDataBar9.AllowNegativeAxis = false;
            formatConditionRuleDataBar9.PredefinedName = "Violet Gradient";
            treeListFormatRule13.Rule = formatConditionRuleDataBar9;
            treeListFormatRule14.Column = this.tlcGDPBy2013;
            treeListFormatRule14.Description = null;
            treeListFormatRule14.Enabled = false;
            treeListFormatRule14.Name = "Year 2013";
            formatConditionRuleDataBar10.AllowNegativeAxis = false;
            formatConditionRuleDataBar10.PredefinedName = "Raspberry Gradient";
            treeListFormatRule14.Rule = formatConditionRuleDataBar10;
            treeListFormatRule15.Column = this.tlcGDPBy2014;
            treeListFormatRule15.Description = null;
            treeListFormatRule15.Enabled = false;
            treeListFormatRule15.Name = "Year 2014";
            formatConditionRuleDataBar11.AllowNegativeAxis = false;
            formatConditionRuleDataBar11.PredefinedName = "Orange Gradient";
            treeListFormatRule15.Rule = formatConditionRuleDataBar11;
            treeListFormatRule16.Column = this.tlcGDPBy2015;
            treeListFormatRule16.Description = null;
            treeListFormatRule16.Name = "Year";
            formatConditionRuleDataBar12.AllowNegativeAxis = false;
            formatConditionRuleDataBar12.PredefinedName = "Orange Gradient";
            treeListFormatRule16.Rule = formatConditionRuleDataBar12;
            this.treeList1.FormatRules.Add(treeListFormatRule1);
            this.treeList1.FormatRules.Add(treeListFormatRule2);
            this.treeList1.FormatRules.Add(treeListFormatRule3);
            this.treeList1.FormatRules.Add(treeListFormatRule4);
            this.treeList1.FormatRules.Add(treeListFormatRule5);
            this.treeList1.FormatRules.Add(treeListFormatRule6);
            this.treeList1.FormatRules.Add(treeListFormatRule7);
            this.treeList1.FormatRules.Add(treeListFormatRule8);
            this.treeList1.FormatRules.Add(treeListFormatRule9);
            this.treeList1.FormatRules.Add(treeListFormatRule10);
            this.treeList1.FormatRules.Add(treeListFormatRule11);
            this.treeList1.FormatRules.Add(treeListFormatRule12);
            this.treeList1.FormatRules.Add(treeListFormatRule13);
            this.treeList1.FormatRules.Add(treeListFormatRule14);
            this.treeList1.FormatRules.Add(treeListFormatRule15);
            this.treeList1.FormatRules.Add(treeListFormatRule16);
            this.treeList1.Location = new System.Drawing.Point(0, 0);
            this.treeList1.Margin = new System.Windows.Forms.Padding(0);
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsSelection.MultiSelect = true;
            this.treeList1.OptionsView.AutoWidth = false;
            this.treeList1.OptionsView.EnableAppearanceEvenRow = true;
            this.treeList1.OptionsView.EnableAppearanceOddRow = true;
            this.treeList1.OptionsView.ShowHorzLines = false;
            this.treeList1.OptionsView.ShowIndicator = false;
            this.treeList1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit3,
            this.repositoryItemTextEdit4});
            this.treeList1.Size = new System.Drawing.Size(531, 432);
            this.treeList1.TabIndex = 1;
            // 
            // treeListBand2
            // 
            this.treeListBand2.Caption = "Continent";
            this.treeListBand2.Columns.Add(this.tlcCountry);
            this.treeListBand2.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Left;
            this.treeListBand2.Name = "treeListBand2";
            // 
            // bcYearsGDPBy2004_2007
            // 
            this.bcYearsGDPBy2004_2007.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2004_2007.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2004_2007.Caption = "2004 - 2007";
            this.bcYearsGDPBy2004_2007.Columns.Add(this.tlcGDPBy2004);
            this.bcYearsGDPBy2004_2007.Columns.Add(this.tlcGDPBy2005);
            this.bcYearsGDPBy2004_2007.Columns.Add(this.tlcGDPBy2006);
            this.bcYearsGDPBy2004_2007.Columns.Add(this.tlcGDPBy2007);
            this.bcYearsGDPBy2004_2007.Name = "bcYearsGDPBy2004_2007";
            this.bcYearsGDPBy2004_2007.Visible = false;
            // 
            // bcYearsGDPBy2008_2009
            // 
            this.bcYearsGDPBy2008_2009.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2008_2009.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2008_2009.Caption = "2008 - 2009";
            this.bcYearsGDPBy2008_2009.Columns.Add(this.tlcGDPBy2008);
            this.bcYearsGDPBy2008_2009.Columns.Add(this.tlcGDPBy2009);
            this.bcYearsGDPBy2008_2009.Name = "bcYearsGDPBy2008_2009";
            // 
            // bcYearsGDPBy2010_2011
            // 
            this.bcYearsGDPBy2010_2011.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2010_2011.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2010_2011.Caption = "2010 - 2011";
            this.bcYearsGDPBy2010_2011.Columns.Add(this.tlcGDPBy2010);
            this.bcYearsGDPBy2010_2011.Columns.Add(this.tlcGDPBy2011);
            this.bcYearsGDPBy2010_2011.Name = "bcYearsGDPBy2010_2011";
            // 
            // bcYearsGDPBy2012_2013
            // 
            this.bcYearsGDPBy2012_2013.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2012_2013.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2012_2013.Caption = "2012 - 2013";
            this.bcYearsGDPBy2012_2013.Columns.Add(this.tlcGDPBy2012);
            this.bcYearsGDPBy2012_2013.Columns.Add(this.tlcGDPBy2013);
            this.bcYearsGDPBy2012_2013.Name = "bcYearsGDPBy2012_2013";
            // 
            // bcYearsGDPBy2014_2015
            // 
            this.bcYearsGDPBy2014_2015.AppearanceHeader.Options.UseTextOptions = true;
            this.bcYearsGDPBy2014_2015.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bcYearsGDPBy2014_2015.Caption = "2014 - 2015";
            this.bcYearsGDPBy2014_2015.Columns.Add(this.tlcGDPBy2014);
            this.bcYearsGDPBy2014_2015.Columns.Add(this.tlcGDPBy2015);
            this.bcYearsGDPBy2014_2015.Name = "bcYearsGDPBy2014_2015";
            // 
            // bcStatistic
            // 
            this.bcStatistic.Caption = "Statistic";
            this.bcStatistic.Columns.Add(this.tlcGDPGrowth);
            this.bcStatistic.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Right;
            this.bcStatistic.Name = "bcStatistic";
            // 
            // sidePanel2
            // 
            this.sidePanel2.Controls.Add(this.btBackToWorld);
            this.sidePanel2.Controls.Add(this.panelControl1);
            this.sidePanel2.Controls.Add(this.mapControl1);
            this.sidePanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.sidePanel2.Location = new System.Drawing.Point(532, 0);
            this.sidePanel2.Name = "sidePanel2";
            this.sidePanel2.Size = new System.Drawing.Size(423, 432);
            this.sidePanel2.TabIndex = 3;
            this.sidePanel2.Text = "sidePanel2";
            // 
            // btBackToWorld
            // 
            this.btBackToWorld.Location = new System.Drawing.Point(11, 14);
            this.btBackToWorld.Name = "btBackToWorld";
            this.btBackToWorld.Size = new System.Drawing.Size(91, 30);
            this.btBackToWorld.TabIndex = 7;
            this.btBackToWorld.Text = "Back to World";
            this.btBackToWorld.Visible = false;
            this.btBackToWorld.Click += new System.EventHandler(this.btBackToWorld_Click);
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.cbeSelectedYear);
            this.panelControl1.Controls.Add(this.lbStatisticByYear);
            this.panelControl1.Location = new System.Drawing.Point(246, 3);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(169, 32);
            this.panelControl1.TabIndex = 6;
            // 
            // cbeSelectedYear
            // 
            this.cbeSelectedYear.Location = new System.Drawing.Point(75, 5);
            this.cbeSelectedYear.Name = "cbeSelectedYear";
            this.cbeSelectedYear.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbeSelectedYear.Size = new System.Drawing.Size(85, 20);
            this.cbeSelectedYear.TabIndex = 0;
            this.cbeSelectedYear.SelectedIndexChanged += new System.EventHandler(this.cbeSelectedYear_SelectedIndexChanged);
            // 
            // lbStatisticByYear
            // 
            this.lbStatisticByYear.Location = new System.Drawing.Point(5, 8);
            this.lbStatisticByYear.Name = "lbStatisticByYear";
            this.lbStatisticByYear.Size = new System.Drawing.Size(64, 13);
            this.lbStatisticByYear.TabIndex = 1;
            this.lbStatisticByYear.Text = "GDP by Year:";
            // 
            // mapControl1
            // 
            this.mapControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.mapControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mapControl1.Layers.Add(this.worldLayer);
            this.mapControl1.Layers.Add(this.allCountriesOnSelectedContinentLayer);
            this.mapControl1.Layers.Add(this.continentLayer);
            this.mapControl1.Location = new System.Drawing.Point(0, 0);
            this.mapControl1.Name = "mapControl1";
            this.mapControl1.NavigationPanelOptions.Visible = false;
            this.mapControl1.SelectionMode = DevExpress.XtraMap.ElementSelectionMode.Single;
            this.mapControl1.Size = new System.Drawing.Size(423, 432);
            this.mapControl1.TabIndex = 4;
            this.mapControl1.MapItemClick += new DevExpress.XtraMap.MapItemClickEventHandler(this.mapControl1_MapItemClick);
            this.worldLayer.Data = this.continentsShapefile;
            this.worldLayer.HighlightedItemStyle.StrokeWidth = 1;
            this.worldLayer.Name = "ContinentLayer";
            this.worldLayer.SelectedItemStyle.StrokeWidth = 1;
            this.worldLayer.ShapeTitlesPattern = "{Continent}";
            this.worldLayer.ShapeTitlesVisibility = DevExpress.XtraMap.VisibilityMode.Visible;
            this.worldLayer.DataLoaded += new DevExpress.XtraMap.DataLoadedEventHandler(this.ContinentLayer_DataLoaded);
            this.allCountriesOnSelectedContinentLayer.HighlightedItemStyle.StrokeWidth = 1;
            this.allCountriesOnSelectedContinentLayer.Name = "AllCountriesLayer";
            this.allCountriesOnSelectedContinentLayer.SelectedItemStyle.StrokeWidth = 1;
            this.continentLayer.HighlightedItemStyle.StrokeWidth = 1;
            this.continentLayer.Name = "TopCountriesLayer";
            this.continentLayer.SelectedItemStyle.StrokeWidth = 1;
            // 
            // StatisticGDP
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.sidePanel2);
            this.Controls.Add(this.sidePanel1);
            this.Name = "StatisticGDP";
            this.Size = new System.Drawing.Size(955, 432);
            this.Load += new System.EventHandler(this.StatisticGDP_Load);
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit3)).EndInit();
            this.sidePanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            this.sidePanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbeSelectedYear.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraMap.ShapefileDataAdapter shapefileDataAdapter1;
        private DevExpress.XtraTreeList.Columns.TreeListBand bcContinent;
        private DevExpress.XtraTreeList.Columns.TreeListBand bcYearsGDPBy2000_2004;
        private DevExpress.XtraTreeList.Columns.TreeListBand bcYearsGDPBy2005_2009;
        private DevExpress.XtraTreeList.Columns.TreeListBand bcYearsGDPBy2010_2014;
        private DevExpress.XtraTreeList.Columns.TreeListBand treeListBand1;
        private XtraEditors.SidePanel sidePanel1;
        private TreeList treeList1;
        private Columns.TreeListColumn tlcCountry;
        private Columns.TreeListColumn tlcGDPBy2004;
        private XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit3;
        private Columns.TreeListColumn tlcGDPBy2005;
        private Columns.TreeListColumn tlcGDPBy2006;
        private Columns.TreeListColumn tlcGDPBy2007;
        private Columns.TreeListColumn tlcGDPBy2008;
        private Columns.TreeListColumn tlcGDPBy2009;
        private Columns.TreeListColumn tlcGDPBy2010;
        private Columns.TreeListColumn tlcGDPBy2011;
        private Columns.TreeListColumn tlcGDPBy2012;
        private Columns.TreeListColumn tlcGDPBy2013;
        private Columns.TreeListColumn tlcGDPBy2014;
        private Columns.TreeListColumn tlcGDPBy2015;
        private Columns.TreeListColumn tlcGDPGrowth;
        private XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit4;
        private XtraEditors.SidePanel sidePanel2;
        private XtraMap.MapControl mapControl1;
        private XtraMap.VectorItemsLayer worldLayer;
        private XtraMap.ShapefileDataAdapter continentsShapefile;
        private XtraMap.VectorItemsLayer allCountriesOnSelectedContinentLayer;
        private XtraMap.VectorItemsLayer continentLayer;
        private XtraEditors.SimpleButton btBackToWorld;
        private XtraEditors.PanelControl panelControl1;
        private XtraEditors.ComboBoxEdit cbeSelectedYear;
        private XtraEditors.LabelControl lbStatisticByYear;
        private Columns.TreeListBand treeListBand2;
        private Columns.TreeListBand bcYearsGDPBy2004_2007;
        private Columns.TreeListBand bcYearsGDPBy2008_2009;
        private Columns.TreeListBand bcYearsGDPBy2010_2011;
        private Columns.TreeListBand bcYearsGDPBy2012_2013;
        private Columns.TreeListBand bcYearsGDPBy2014_2015;
        private Columns.TreeListBand bcStatistic;
    }
}
