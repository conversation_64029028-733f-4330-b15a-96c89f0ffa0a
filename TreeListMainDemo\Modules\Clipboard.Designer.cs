﻿namespace DevExpress.XtraTreeList.Demos {
    partial class ClipboardFormats {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule1 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom1 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule2 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom2 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule3 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleContains formatConditionRuleContains1 = new DevExpress.XtraEditors.FormatConditionRuleContains();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule4 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue1 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule5 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue2 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule6 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue3 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule7 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue4 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule8 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom3 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule9 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom4 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule10 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom5 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule treeListFormatRule11 = new DevExpress.XtraTreeList.StyleFormatConditions.TreeListFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleTopBottom formatConditionRuleTopBottom6 = new DevExpress.XtraEditors.FormatConditionRuleTopBottom();
            this.colMarketShare = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colRegion = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colSeptSalesPrev = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colMarchSalesPrev = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colSeptSales = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.colMarchSales = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.treeList1 = new DevExpress.XtraTreeList.Demos.ClipboardTreeListControl();
            this.treeListBand1 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.treeListBand2 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.treeListBand3 = new DevExpress.XtraTreeList.Columns.TreeListBand();
            this.simpleButtonCopyPaste = new DevExpress.XtraEditors.SimpleButton();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemTextEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemTextEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.spreadsheetControl1 = new DevExpress.XtraSpreadsheet.SpreadsheetControl();
            this.richEditControl = new DevExpress.XtraRichEdit.RichEditControl();
            this.propertyGridControl = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.webBrowser1 = new System.Windows.Forms.WebBrowser();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.richEditLCI = new DevExpress.XtraLayout.LayoutControlItem();
            this.gridViewPropertiesLayoutGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem8 = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitterItem1 = new DevExpress.XtraLayout.SplitterItem();
            this.webBrowserLCI = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitterItemGrid = new DevExpress.XtraLayout.SplitterItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.copyPasteButtonLCI = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.richEditLCI)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPropertiesLayoutGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.webBrowserLCI)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItemGrid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.copyPasteButtonLCI)).BeginInit();
            this.SuspendLayout();
            // 
            // colMarketShare
            // 
            this.colMarketShare.Caption = "Market Share";
            this.colMarketShare.FieldName = "MarketShare";
            this.colMarketShare.Format.FormatString = "p0";
            this.colMarketShare.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colMarketShare.Name = "colMarketShare";
            this.colMarketShare.Visible = true;
            this.colMarketShare.VisibleIndex = 5;
            this.colMarketShare.Width = 61;
            // 
            // colRegion
            // 
            this.colRegion.Caption = " Region";
            this.colRegion.FieldName = "Region";
            this.colRegion.Name = "colRegion";
            this.colRegion.Visible = true;
            this.colRegion.VisibleIndex = 0;
            this.colRegion.Width = 46;
            // 
            // colSeptSalesPrev
            // 
            this.colSeptSalesPrev.Caption = "September";
            this.colSeptSalesPrev.FieldName = "September From Prior Year";
            this.colSeptSalesPrev.Format.FormatString = "p";
            this.colSeptSalesPrev.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSeptSalesPrev.Name = "colSeptSalesPrev";
            this.colSeptSalesPrev.UnboundExpression = "([SeptemberSales] - [SeptemberSalesPrev]) / Iif([SeptemberSales] = 0, 1, [Septemb" +
    "erSales])";
            this.colSeptSalesPrev.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.colSeptSalesPrev.Visible = true;
            this.colSeptSalesPrev.VisibleIndex = 4;
            this.colSeptSalesPrev.Width = 53;
            // 
            // colMarchSalesPrev
            // 
            this.colMarchSalesPrev.Caption = "March";
            this.colMarchSalesPrev.FieldName = "March From Prior Year";
            this.colMarchSalesPrev.Format.FormatString = "p";
            this.colMarchSalesPrev.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colMarchSalesPrev.Name = "colMarchSalesPrev";
            this.colMarchSalesPrev.UnboundExpression = "([MarchSales] - [MarchSalesPrev]) / Iif([MarchSales] = 0, 1, [MarchSales])";
            this.colMarchSalesPrev.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal;
            this.colMarchSalesPrev.Visible = true;
            this.colMarchSalesPrev.VisibleIndex = 3;
            this.colMarchSalesPrev.Width = 51;
            // 
            // colSeptSales
            // 
            this.colSeptSales.Caption = "September";
            this.colSeptSales.FieldName = "SeptemberSales";
            this.colSeptSales.Name = "colSeptSales";
            this.colSeptSales.Visible = true;
            this.colSeptSales.VisibleIndex = 2;
            this.colSeptSales.Width = 46;
            // 
            // colMarchSales
            // 
            this.colMarchSales.Caption = "March";
            this.colMarchSales.FieldName = "MarchSales";
            this.colMarchSales.Name = "colMarchSales";
            this.colMarchSales.Visible = true;
            this.colMarchSales.VisibleIndex = 1;
            this.colMarchSales.Width = 44;
            // 
            // treeList
            // 
            this.treeList1.Bands.AddRange(new DevExpress.XtraTreeList.Columns.TreeListBand[] {
            this.treeListBand1,
            this.treeListBand2,
            this.treeListBand3});
            this.treeList1.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.colRegion,
            this.colMarchSales,
            this.colSeptSales,
            this.colMarchSalesPrev,
            this.colSeptSalesPrev,
            this.colMarketShare});
            this.treeList1.Cursor = System.Windows.Forms.Cursors.Default;
            treeListFormatRule1.Column = this.colMarketShare;
            treeListFormatRule1.Name = "Market Share Bottom";
            formatConditionRuleTopBottom1.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom1.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom1.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            formatConditionRuleTopBottom1.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom1.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom1.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom1.Rank = new decimal(new int[] {
            20,
            0,
            0,
            0});
            formatConditionRuleTopBottom1.RankType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            formatConditionRuleTopBottom1.TopBottom = DevExpress.XtraEditors.FormatConditionTopBottomType.Bottom;
            treeListFormatRule1.Rule = formatConditionRuleTopBottom1;
            treeListFormatRule2.Column = this.colMarketShare;
            treeListFormatRule2.Name = "Market Share Top";
            formatConditionRuleTopBottom2.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom2.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom2.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            formatConditionRuleTopBottom2.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom2.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom2.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom2.Rank = new decimal(new int[] {
            20,
            0,
            0,
            0});
            formatConditionRuleTopBottom2.RankType = DevExpress.XtraEditors.FormatConditionValueType.Percent;
            treeListFormatRule2.Rule = formatConditionRuleTopBottom2;
            treeListFormatRule3.ApplyToRow = true;
            treeListFormatRule3.Column = this.colRegion;
            treeListFormatRule3.Name = "Region Name";
            formatConditionRuleContains1.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleContains1.Appearance.Options.UseFont = true;
            formatConditionRuleContains1.Values = new object[] {
        ((object)("Western Europe")),
        ((object)("Eastern Europe")),
        ((object)("North America")),
        ((object)("South America")),
        ((object)("Asia"))};
            treeListFormatRule3.Rule = formatConditionRuleContains1;
            treeListFormatRule4.Column = this.colSeptSalesPrev;
            treeListFormatRule4.Name = "Change Sept Positive";
            formatConditionRuleValue1.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            formatConditionRuleValue1.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue1.Condition = DevExpress.XtraEditors.FormatCondition.Greater;
            formatConditionRuleValue1.Value1 = "0";
            treeListFormatRule4.Rule = formatConditionRuleValue1;
            treeListFormatRule5.Column = this.colSeptSalesPrev;
            treeListFormatRule5.Name = "Change Sept Negative";
            formatConditionRuleValue2.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            formatConditionRuleValue2.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue2.Condition = DevExpress.XtraEditors.FormatCondition.Less;
            formatConditionRuleValue2.Value1 = "0";
            treeListFormatRule5.Rule = formatConditionRuleValue2;
            treeListFormatRule6.Column = this.colMarchSalesPrev;
            treeListFormatRule6.Name = "Change March Positive";
            formatConditionRuleValue3.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            formatConditionRuleValue3.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue3.Condition = DevExpress.XtraEditors.FormatCondition.Greater;
            formatConditionRuleValue3.Value1 = "0";
            treeListFormatRule6.Rule = formatConditionRuleValue3;
            treeListFormatRule7.Column = this.colMarchSalesPrev;
            treeListFormatRule7.Name = "Change March Negative";
            formatConditionRuleValue4.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            formatConditionRuleValue4.Appearance.Options.UseForeColor = true;
            formatConditionRuleValue4.Condition = DevExpress.XtraEditors.FormatCondition.Less;
            formatConditionRuleValue4.Value1 = "0";
            treeListFormatRule7.Rule = formatConditionRuleValue4;
            treeListFormatRule8.Column = this.colSeptSales;
            treeListFormatRule8.Name = "Sales Sept Top";
            formatConditionRuleTopBottom3.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom3.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom3.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            formatConditionRuleTopBottom3.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom3.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom3.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom3.Rank = new decimal(new int[] {
            5,
            0,
            0,
            0});
            treeListFormatRule8.Rule = formatConditionRuleTopBottom3;
            treeListFormatRule9.Column = this.colMarchSales;
            treeListFormatRule9.Name = "Sales March Top";
            formatConditionRuleTopBottom4.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom4.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom4.Appearance.ForeColor = System.Drawing.Color.DarkGreen;
            formatConditionRuleTopBottom4.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom4.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom4.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom4.Rank = new decimal(new int[] {
            5,
            0,
            0,
            0});
            treeListFormatRule9.Rule = formatConditionRuleTopBottom4;
            treeListFormatRule10.Column = this.colMarchSales;
            treeListFormatRule10.Name = "Sales March Bottom";
            formatConditionRuleTopBottom5.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom5.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom5.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            formatConditionRuleTopBottom5.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom5.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom5.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom5.Rank = new decimal(new int[] {
            5,
            0,
            0,
            0});
            formatConditionRuleTopBottom5.TopBottom = DevExpress.XtraEditors.FormatConditionTopBottomType.Bottom;
            treeListFormatRule10.Rule = formatConditionRuleTopBottom5;
            treeListFormatRule11.Column = this.colSeptSales;
            treeListFormatRule11.Name = "Sales Sept Bottom";
            formatConditionRuleTopBottom6.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            formatConditionRuleTopBottom6.Appearance.FontStyleDelta = System.Drawing.FontStyle.Bold;
            formatConditionRuleTopBottom6.Appearance.ForeColor = System.Drawing.Color.DarkRed;
            formatConditionRuleTopBottom6.Appearance.Options.UseBackColor = true;
            formatConditionRuleTopBottom6.Appearance.Options.UseFont = true;
            formatConditionRuleTopBottom6.Appearance.Options.UseForeColor = true;
            formatConditionRuleTopBottom6.Rank = new decimal(new int[] {
            5,
            0,
            0,
            0});
            formatConditionRuleTopBottom6.TopBottom = DevExpress.XtraEditors.FormatConditionTopBottomType.Bottom;
            treeListFormatRule11.Rule = formatConditionRuleTopBottom6;
            this.treeList1.FormatRules.Add(treeListFormatRule1);
            this.treeList1.FormatRules.Add(treeListFormatRule2);
            this.treeList1.FormatRules.Add(treeListFormatRule3);
            this.treeList1.FormatRules.Add(treeListFormatRule4);
            this.treeList1.FormatRules.Add(treeListFormatRule5);
            this.treeList1.FormatRules.Add(treeListFormatRule6);
            this.treeList1.FormatRules.Add(treeListFormatRule7);
            this.treeList1.FormatRules.Add(treeListFormatRule8);
            this.treeList1.FormatRules.Add(treeListFormatRule9);
            this.treeList1.FormatRules.Add(treeListFormatRule10);
            this.treeList1.FormatRules.Add(treeListFormatRule11);
            this.treeList1.Location = new System.Drawing.Point(2, 28);
            this.treeList1.Name = "treeList1";
            this.treeList1.OptionsClipboard.AllowHtmlFormat = DevExpress.Utils.DefaultBoolean.True;
            this.treeList1.OptionsClipboard.ClipboardMode = DevExpress.Export.ClipboardMode.Formatted;
            this.treeList1.OptionsClipboard.CopyCollapsedData = DevExpress.Utils.DefaultBoolean.True;
            this.treeList1.OptionsClipboard.PasteMode = DevExpress.Export.PasteMode.Append;
            this.treeList1.OptionsMenu.ShowConditionalFormattingItem = true;
            this.treeList1.OptionsSelection.MultiSelect = true;
            this.treeList1.OptionsSelection.MultiSelectMode = DevExpress.XtraTreeList.TreeListMultiSelectMode.CellSelect;
            this.treeList1.OptionsView.AllowHtmlDrawHeaders = true;
            this.treeList1.OptionsView.AnimationType = DevExpress.XtraTreeList.TreeListAnimationType.AnimateAllContent;
            this.treeList1.OptionsView.EnableAppearanceOddRow = true;
            this.treeList1.OptionsView.ShowBandsMode = DevExpress.Utils.DefaultBoolean.True;
            this.treeList1.ParentFieldName = "RegionID";
            this.treeList1.Size = new System.Drawing.Size(346, 522);
            this.treeList1.TabIndex = 13;
            this.treeList1.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.treeList_ShowingEditor);
            // 
            // treeListBand1
            // 
            this.treeListBand1.Caption = "<b>Sales</b>";
            this.treeListBand1.Columns.Add(this.colRegion);
            this.treeListBand1.Columns.Add(this.colMarchSales);
            this.treeListBand1.Columns.Add(this.colSeptSales);
            this.treeListBand1.Name = "treeListBand1";
            this.treeListBand1.Width = 136;
            // 
            // treeListBand2
            // 
            this.treeListBand2.Caption = "<b>Change From Prior Year</b>";
            this.treeListBand2.Columns.Add(this.colMarchSalesPrev);
            this.treeListBand2.Columns.Add(this.colSeptSalesPrev);
            this.treeListBand2.Name = "treeListBand2";
            this.treeListBand2.Width = 104;
            // 
            // treeListBand3
            // 
            this.treeListBand3.Caption = "<b>Current Market Share </b>";
            this.treeListBand3.Columns.Add(this.colMarketShare);
            this.treeListBand3.Name = "treeListBand3";
            this.treeListBand3.Width = 61;
            // 
            // simpleButtonCopyPaste
            // 
            this.simpleButtonCopyPaste.Location = new System.Drawing.Point(-55, -63);
            this.simpleButtonCopyPaste.Name = "simpleButtonCopyPaste";
            this.simpleButtonCopyPaste.Size = new System.Drawing.Size(272, 22);
            this.simpleButtonCopyPaste.TabIndex = 20;
            this.simpleButtonCopyPaste.Text = "Copy and Paste";
            // 
            // repositoryItemSpinEdit1
            // 
            this.repositoryItemSpinEdit1.AutoHeight = false;
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            // 
            // repositoryItemTextEdit1
            // 
            this.repositoryItemTextEdit1.AutoHeight = false;
            this.repositoryItemTextEdit1.Mask.EditMask = "p";
            this.repositoryItemTextEdit1.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = true;
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // repositoryItemTextEdit2
            // 
            this.repositoryItemTextEdit2.AutoHeight = false;
            this.repositoryItemTextEdit2.Mask.EditMask = "p0";
            this.repositoryItemTextEdit2.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.repositoryItemTextEdit2.Mask.UseMaskAsDisplayFormat = true;
            this.repositoryItemTextEdit2.Name = "repositoryItemTextEdit2";
            // 
            // repositoryItemTextEdit3
            // 
            this.repositoryItemTextEdit3.AutoHeight = false;
            this.repositoryItemTextEdit3.Mask.EditMask = "n";
            this.repositoryItemTextEdit3.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.repositoryItemTextEdit3.Name = "repositoryItemTextEdit3";
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.treeList1);
            this.layoutControl1.Controls.Add(this.spreadsheetControl1);
            this.layoutControl1.Controls.Add(this.richEditControl);
            this.layoutControl1.Controls.Add(this.propertyGridControl);
            this.layoutControl1.Controls.Add(this.panelControl1);
            this.layoutControl1.Controls.Add(this.simpleButton1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(-1064, 368, 950, 805);
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(985, 552);
            this.layoutControl1.TabIndex = 21;
            this.layoutControl1.Text = "layoutControl1";
            this.layoutControl1.GroupExpandChanged += new DevExpress.XtraLayout.Utils.LayoutGroupEventHandler(this.layoutControl1_GroupExpandChanged);
            // 
            // spreadsheetControl1
            // 
            this.spreadsheetControl1.Location = new System.Drawing.Point(357, 18);
            this.spreadsheetControl1.Name = "spreadsheetControl1";
            this.spreadsheetControl1.Options.TabSelector.Visibility = DevExpress.XtraSpreadsheet.SpreadsheetElementVisibility.Hidden;
            this.spreadsheetControl1.Options.View.Charts.Antialiasing = DevExpress.XtraSpreadsheet.DocumentCapability.Enabled;
            this.spreadsheetControl1.Options.View.Charts.TextAntialiasing = DevExpress.XtraSpreadsheet.DocumentCapability.Enabled;
            this.spreadsheetControl1.Options.View.ShowColumnHeaders = false;
            this.spreadsheetControl1.Options.View.ShowPrintArea = false;
            this.spreadsheetControl1.Options.View.ShowRowHeaders = false;
            this.spreadsheetControl1.Size = new System.Drawing.Size(292, 164);
            this.spreadsheetControl1.TabIndex = 4;
            this.spreadsheetControl1.Text = "spreadsheetControl1";
            // 
            // richEditControl
            // 
            this.richEditControl.ActiveViewType = DevExpress.XtraRichEdit.RichEditViewType.Draft;
            this.richEditControl.LayoutUnit = DevExpress.XtraRichEdit.DocumentLayoutUnit.Pixel;
            this.richEditControl.Location = new System.Drawing.Point(357, 202);
            this.richEditControl.Name = "richEditControl";
            this.richEditControl.Options.HorizontalRuler.Visibility = DevExpress.XtraRichEdit.RichEditRulerVisibility.Hidden;
            this.richEditControl.Options.VerticalRuler.Visibility = DevExpress.XtraRichEdit.RichEditRulerVisibility.Hidden;
            this.richEditControl.Size = new System.Drawing.Size(292, 164);
            this.richEditControl.TabIndex = 12;
            this.richEditControl.Views.DraftView.Padding = new DevExpress.Portable.PortablePadding(17, 16, 0, 0);
            // 
            // propertyGridControl
            // 
            this.propertyGridControl.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.propertyGridControl.Cursor = System.Windows.Forms.Cursors.Hand;
            this.propertyGridControl.Location = new System.Drawing.Point(661, 4);
            this.propertyGridControl.MinimumSize = new System.Drawing.Size(300, 0);
            this.propertyGridControl.Name = "propertyGridControl";
            this.propertyGridControl.OptionsView.ShowRootCategories = false;
            this.propertyGridControl.Size = new System.Drawing.Size(300, 543);
            this.propertyGridControl.TabIndex = 17;
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.webBrowser1);
            this.panelControl1.Location = new System.Drawing.Point(357, 386);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(292, 164);
            this.panelControl1.TabIndex = 19;
            // 
            // webBrowser1
            // 
            this.webBrowser1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.webBrowser1.Location = new System.Drawing.Point(2, 2);
            this.webBrowser1.MinimumSize = new System.Drawing.Size(20, 20);
            this.webBrowser1.Name = "webBrowser1";
            this.webBrowser1.Size = new System.Drawing.Size(288, 160);
            this.webBrowser1.TabIndex = 13;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.BackColor = DevExpress.LookAndFeel.DXSkinColors.FillColors.Success;
            this.simpleButton1.Appearance.Options.UseBackColor = true;
            this.simpleButton1.Location = new System.Drawing.Point(2, 2);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(346, 22);
            this.simpleButton1.StyleController = this.layoutControl1;
            this.simpleButton1.TabIndex = 15;
            this.simpleButton1.Text = "Copy and Paste";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButtonCopyPaste_Click);
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2,
            this.richEditLCI,
            this.gridViewPropertiesLayoutGroup,
            this.splitterItem1,
            this.webBrowserLCI,
            this.splitterItemGrid,
            this.layoutControlItem1,
            this.copyPasteButtonLCI});
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup1.Size = new System.Drawing.Size(985, 552);
            this.layoutControlGroup1.TextVisible = false;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.spreadsheetControl1;
            this.layoutControlItem2.CustomizationFormText = "Excel format";
            this.layoutControlItem2.Location = new System.Drawing.Point(355, 0);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(296, 184);
            this.layoutControlItem2.Text = "Excel format";
            this.layoutControlItem2.TextLocation = DevExpress.Utils.Locations.Top;
            this.layoutControlItem2.TextSize = new System.Drawing.Size(78, 13);
            // 
            // richEditLCI
            // 
            this.richEditLCI.Control = this.richEditControl;
            this.richEditLCI.CustomizationFormText = "Rich text format";
            this.richEditLCI.Location = new System.Drawing.Point(355, 184);
            this.richEditLCI.Name = "richEditLCI";
            this.richEditLCI.Size = new System.Drawing.Size(296, 184);
            this.richEditLCI.Text = "Rich text format";
            this.richEditLCI.TextLocation = DevExpress.Utils.Locations.Top;
            this.richEditLCI.TextSize = new System.Drawing.Size(78, 13);
            // 
            // gridViewPropertiesLayoutGroup
            // 
            this.gridViewPropertiesLayoutGroup.CustomizationFormText = "Clipboard Options";
            this.gridViewPropertiesLayoutGroup.ExpandButtonVisible = true;
            this.gridViewPropertiesLayoutGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem8});
            this.gridViewPropertiesLayoutGroup.Location = new System.Drawing.Point(656, 0);
            this.gridViewPropertiesLayoutGroup.Name = "gridViewPropertiesLayoutGroup";
            this.gridViewPropertiesLayoutGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.gridViewPropertiesLayoutGroup.Size = new System.Drawing.Size(329, 552);
            this.gridViewPropertiesLayoutGroup.Text = "Clipboard Options";
            this.gridViewPropertiesLayoutGroup.TextLocation = DevExpress.Utils.Locations.Right;
            // 
            // layoutControlItem8
            // 
            this.layoutControlItem8.Control = this.propertyGridControl;
            this.layoutControlItem8.CustomizationFormText = "layoutControlItem8";
            this.layoutControlItem8.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem8.Name = "layoutControlItem8";
            this.layoutControlItem8.Size = new System.Drawing.Size(304, 547);
            this.layoutControlItem8.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem8.TextVisible = false;
            // 
            // splitterItemProperties
            // 
            this.splitterItem1.AllowHotTrack = true;
            this.splitterItem1.CustomizationFormText = "splitterItemProperties";
            this.splitterItem1.Location = new System.Drawing.Point(651, 0);
            this.splitterItem1.Name = "splitterItemProperties";
            this.splitterItem1.Size = new System.Drawing.Size(5, 552);
            this.splitterItem1.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            // 
            // webBrowserLCI
            // 
            this.webBrowserLCI.Control = this.panelControl1;
            this.webBrowserLCI.CustomizationFormText = "HTML format";
            this.webBrowserLCI.Location = new System.Drawing.Point(355, 368);
            this.webBrowserLCI.Name = "webBrowserLCI";
            this.webBrowserLCI.Size = new System.Drawing.Size(296, 184);
            this.webBrowserLCI.Text = "HTML format";
            this.webBrowserLCI.TextLocation = DevExpress.Utils.Locations.Top;
            this.webBrowserLCI.TextSize = new System.Drawing.Size(78, 13);
            // 
            // splitterItemGrid
            // 
            this.splitterItemGrid.AllowHotTrack = true;
            this.splitterItemGrid.CustomizationFormText = "splitterItemGrid";
            this.splitterItemGrid.Location = new System.Drawing.Point(350, 0);
            this.splitterItemGrid.Name = "splitterItemGrid";
            this.splitterItemGrid.Size = new System.Drawing.Size(5, 552);
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.treeList1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 26);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(350, 526);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // copyPasteButtonLCI
            // 
            this.copyPasteButtonLCI.Control = this.simpleButton1;
            this.copyPasteButtonLCI.CustomizationFormText = "copyPasteButtonLCI";
            this.copyPasteButtonLCI.Location = new System.Drawing.Point(0, 0);
            this.copyPasteButtonLCI.Name = "copyPasteButtonLCI";
            this.copyPasteButtonLCI.Size = new System.Drawing.Size(350, 26);
            this.copyPasteButtonLCI.TextSize = new System.Drawing.Size(0, 0);
            this.copyPasteButtonLCI.TextVisible = false;
            // 
            // ClipboardFormats
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.layoutControl1);
            this.Controls.Add(this.simpleButtonCopyPaste);
            this.Name = "ClipboardFormats";
            this.Size = new System.Drawing.Size(985, 552);
            this.Load += new System.EventHandler(this.ClipboardFormats_Load);
            ((System.ComponentModel.ISupportInitialize)(this.treeList1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.richEditLCI)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewPropertiesLayoutGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.webBrowserLCI)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItemGrid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.copyPasteButtonLCI)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private ClipboardTreeListControl treeList1;
        private Columns.TreeListColumn colRegion;
        private Columns.TreeListColumn colMarchSales;
        private Columns.TreeListColumn colSeptSales;
        private Columns.TreeListColumn colMarchSalesPrev;
        private Columns.TreeListColumn colSeptSalesPrev;
        private Columns.TreeListColumn colMarketShare;
        private Columns.TreeListBand treeListBand1;
        private Columns.TreeListBand treeListBand2;
        private Columns.TreeListBand treeListBand3;
        private XtraEditors.SimpleButton simpleButtonCopyPaste;
        private XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit2;
        private XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit3;
        private XtraLayout.LayoutControl layoutControl1;
        private XtraSpreadsheet.SpreadsheetControl spreadsheetControl1;
        private XtraRichEdit.RichEditControl richEditControl;
        private XtraVerticalGrid.PropertyGridControl propertyGridControl;
        private XtraEditors.PanelControl panelControl1;
        private System.Windows.Forms.WebBrowser webBrowser1;
        private XtraEditors.SimpleButton simpleButton1;
        private XtraLayout.LayoutControlGroup layoutControlGroup1;
        private XtraLayout.LayoutControlItem layoutControlItem2;
        private XtraLayout.LayoutControlItem richEditLCI;
        private XtraLayout.LayoutControlGroup gridViewPropertiesLayoutGroup;
        private XtraLayout.LayoutControlItem layoutControlItem8;
        private XtraLayout.SplitterItem splitterItem1;
        private XtraLayout.LayoutControlItem webBrowserLCI;
        private XtraLayout.SplitterItem splitterItemGrid;
        private XtraLayout.LayoutControlItem layoutControlItem1;
        private XtraLayout.LayoutControlItem copyPasteButtonLCI;
    }
}
