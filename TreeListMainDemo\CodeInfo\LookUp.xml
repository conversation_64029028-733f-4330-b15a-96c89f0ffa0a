<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
  <controls>
    <controlentry>
      <control>
        <name>treeListLookUpEdit1</name>
        <windowcaption>Standalone TreeListLookUpEdit</windowcaption>
        <description>The TreeListLookUpEdit control provides lookup functionality using a dropdown XtraTreeList control.</description>
        <memberlist>TreeListLookUpEdit Class</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>treeList1</name>
        <windowcaption>In-place TreeListLookUpEdit editor</windowcaption>
        <description>The Owner column uses a TreeListLookUpEdit control as an in-place editor.</description>
        <memberlist>RepositoryItemTreeListLookUpEdit Class</memberlist>
        <dtimage/>
      </control>
    </controlentry>

    <controlentry>
      <control>
        <name>icbTextEditStyle</name>
        <windowcaption>Text edit style</windowcaption>
        <description>Specifies the style in which text is displayed and edited in the editor.</description>
        <memberlist>RepositoryItemSearchLookUpEdit.TextEditStyle</memberlist>
        <dtimage/>
      </control>
    </controlentry>

    <controlentry>
      <control>
        <name>ceAutoComplete</name>
        <windowcaption>Automatic completion</windowcaption>
        <description>In auto completion mode, the text in the edit box is automatically completed if it matches a DisplayMember field value of one of the dropdown rows.</description>
        <memberlist>RepositoryItemTreeListLookUpEdit.AutoComplete</memberlist>
        <dtimage/>
      </control>
    </controlentry>

    <controlentry>
      <control>
        <name>ceImmediatePopup</name>
        <windowcaption>Immediate popup</windowcaption>
        <description>Specifies whether the popup window is displayed immediately after an end-user has typed a character in the edit box.</description>
        <memberlist>RepositoryItemPopupBaseAutoSearchEdit.ImmediatePopup</memberlist>
        <dtimage/>
      </control>
    </controlentry>

    <controlentry>
      <control>
        <name>cePopupSizeable</name>
        <windowcaption>Sizable popup window</windowcaption>
        <description>Specifies whether the popup window is sizable.</description>
        <memberlist>RepositoryItemLookUpEditBase.PopupSizeable</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceShowPopupFooter</name>
        <windowcaption>Popup footer</windowcaption>
        <description>Specifies whether the dropdown window's footer is visible.</description>
        <memberlist>RepositoryItemLookUpEditBase.ShowFooter</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceShowAutoFilterRow</name>
        <windowcaption>Auto filter row</windowcaption>
        <description>Specifies whether the auto filter row is displayed.</description>
        <memberlist>TreeListOptionsView.ShowAutoFilterRow</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceShowColumnHeaders</name>
        <windowcaption>Showing column headers</windowcaption>
        <description>Specifies whether column headers are displayed.</description>
        <memberlist>TreeListOptionsView.ShowColumns</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceShowIndicator</name>
        <windowcaption>Showing indicator panel</windowcaption>
        <description>Specifies whether the row indicator panel is displayed.</description>
        <memberlist>TreeListOptionsView.ShowIndicator</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceEnableAppearanceEvenRow</name>
        <windowcaption>Even row appearance</windowcaption>
        <description>Specifies whether the even rows are painted using the appearance settings provided by the TreeListAppearanceCollection.EvenRow property.</description>
        <memberlist>TreeListOptionsView.EnableAppearanceEvenRow</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>ceEnableAppearanceOddRow</name>
        <windowcaption>Odd row appearance</windowcaption>
        <description>Specifies whether the odd rows are painted using the appearance settings provided by the TreeListAppearanceCollection.OddRow property.</description>
        <memberlist>TreeListOptionsView.EnableAppearanceOddRow</memberlist>
        <dtimage/>
      </control>
    </controlentry>




  </controls>
</totalinfo>