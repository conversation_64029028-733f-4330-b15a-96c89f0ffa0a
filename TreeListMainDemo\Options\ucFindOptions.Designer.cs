﻿namespace DevExpress.XtraTreeList.Demos.Options  {
    partial class ucFindOptions {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.layoutControl = new DevExpress.XtraLayout.LayoutControl();
            this.imgParserKind = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.ceShowSearchNavButtons = new DevExpress.XtraEditors.CheckEdit();
            this.ceExpandNodesOnSearch = new DevExpress.XtraEditors.CheckEdit();
            this.imgBehavior = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.ceShowCloseButton = new DevExpress.XtraEditors.CheckEdit();
            this.ceShowClearButton = new DevExpress.XtraEditors.CheckEdit();
            this.ceShowFindButton = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowFindPanel = new DevExpress.XtraEditors.CheckEdit();
            this.ceAlwaysVisible = new DevExpress.XtraEditors.CheckEdit();
            this.ceClearFindPanelOnClose = new DevExpress.XtraEditors.CheckEdit();
            this.ceHighlightFindResults = new DevExpress.XtraEditors.CheckEdit();
            this.seFindDelay = new DevExpress.XtraEditors.SpinEdit();
            this.cbFindMode = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.cbFindFilterColumns = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.imgFilterCondition = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.layoutControlGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lgBehavior = new DevExpress.XtraLayout.LayoutControlGroup();
            this.behaviorLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.parserKindLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.filterConditionLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.customizationLayoutGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.liShowCloseButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.liShowClearButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.liShowFindButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.showSearchNavButtonsLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.expandNodesOnSearchLayoutItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowFindPanel = new DevExpress.XtraLayout.LayoutControlItem();
            this.liHighlightFindResults = new DevExpress.XtraLayout.LayoutControlItem();
            this.actionLayoutGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.liFindDelay = new DevExpress.XtraLayout.LayoutControlItem();
            this.liFindFilterColumns = new DevExpress.XtraLayout.LayoutControlItem();
            this.liMode = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAlwaysVisible = new DevExpress.XtraLayout.LayoutControlItem();
            this.liClearFindPanelOnClose = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl)).BeginInit();
            this.layoutControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.imgParserKind.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowSearchNavButtons.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceExpandNodesOnSearch.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imgBehavior.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowCloseButton.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowClearButton.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowFindButton.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowFindPanel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAlwaysVisible.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceClearFindPanelOnClose.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceHighlightFindResults.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seFindDelay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFindMode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFindFilterColumns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imgFilterCondition.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgBehavior)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.parserKindLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.filterConditionLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.customizationLayoutGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowCloseButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowClearButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowFindButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.showSearchNavButtonsLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.expandNodesOnSearchLayoutItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowFindPanel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liHighlightFindResults)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.actionLayoutGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liFindDelay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liFindFilterColumns)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liMode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAlwaysVisible)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liClearFindPanelOnClose)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl
            // 
            this.layoutControl.Controls.Add(this.imgParserKind);
            this.layoutControl.Controls.Add(this.ceShowSearchNavButtons);
            this.layoutControl.Controls.Add(this.ceExpandNodesOnSearch);
            this.layoutControl.Controls.Add(this.imgBehavior);
            this.layoutControl.Controls.Add(this.ceShowCloseButton);
            this.layoutControl.Controls.Add(this.ceShowClearButton);
            this.layoutControl.Controls.Add(this.ceShowFindButton);
            this.layoutControl.Controls.Add(this.ceAllowFindPanel);
            this.layoutControl.Controls.Add(this.ceAlwaysVisible);
            this.layoutControl.Controls.Add(this.ceClearFindPanelOnClose);
            this.layoutControl.Controls.Add(this.ceHighlightFindResults);
            this.layoutControl.Controls.Add(this.seFindDelay);
            this.layoutControl.Controls.Add(this.cbFindMode);
            this.layoutControl.Controls.Add(this.cbFindFilterColumns);
            this.layoutControl.Controls.Add(this.imgFilterCondition);
            this.layoutControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl.Location = new System.Drawing.Point(0, 0);
            this.layoutControl.Name = "layoutControl";
            this.layoutControl.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(820, 56, 1330, 965);
            this.layoutControl.Root = this.layoutControlGroup;
            this.layoutControl.Size = new System.Drawing.Size(285, 509);
            this.layoutControl.TabIndex = 23;
            this.layoutControl.Text = "layoutControl";
            // 
            // imgParserKind
            // 
            this.imgParserKind.Location = new System.Drawing.Point(100, 58);
            this.imgParserKind.Name = "imgParserKind";
            this.imgParserKind.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.imgParserKind.Size = new System.Drawing.Size(173, 20);
            this.imgParserKind.StyleController = this.layoutControl;
            this.imgParserKind.TabIndex = 19;
            // 
            // ceShowSearchNavButtons
            // 
            this.ceShowSearchNavButtons.Location = new System.Drawing.Point(12, 270);
            this.ceShowSearchNavButtons.Name = "ceShowSearchNavButtons";
            this.ceShowSearchNavButtons.Properties.Caption = "Show Prev/Next Buttons";
            this.ceShowSearchNavButtons.Size = new System.Drawing.Size(261, 20);
            this.ceShowSearchNavButtons.StyleController = this.layoutControl;
            this.ceShowSearchNavButtons.TabIndex = 18;
            // 
            // ceExpandNodesOnSearch
            // 
            this.ceExpandNodesOnSearch.Location = new System.Drawing.Point(12, 294);
            this.ceExpandNodesOnSearch.Name = "ceExpandNodesOnSearch";
            this.ceExpandNodesOnSearch.Properties.Caption = "Expand Nodes on Search";
            this.ceExpandNodesOnSearch.Size = new System.Drawing.Size(261, 20);
            this.ceExpandNodesOnSearch.StyleController = this.layoutControl;
            this.ceExpandNodesOnSearch.TabIndex = 18;
            this.ceExpandNodesOnSearch.CheckedChanged += new System.EventHandler(this.ceExpandNodesOnSearch_CheckedChanged);
            // 
            // imgBehavior
            // 
            this.imgBehavior.Location = new System.Drawing.Point(100, 34);
            this.imgBehavior.Name = "imgBehavior";
            this.imgBehavior.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.imgBehavior.Size = new System.Drawing.Size(173, 20);
            this.imgBehavior.StyleController = this.layoutControl;
            this.imgBehavior.TabIndex = 17;
            this.imgBehavior.SelectedIndexChanged += new System.EventHandler(this.imgBehavior_SelectedIndexChanged);
            // 
            // ceShowCloseButton
            // 
            this.ceShowCloseButton.Location = new System.Drawing.Point(12, 198);
            this.ceShowCloseButton.Name = "ceShowCloseButton";
            this.ceShowCloseButton.Properties.Caption = "Show Close Button";
            this.ceShowCloseButton.Size = new System.Drawing.Size(261, 20);
            this.ceShowCloseButton.StyleController = this.layoutControl;
            this.ceShowCloseButton.TabIndex = 16;
            // 
            // ceShowClearButton
            // 
            this.ceShowClearButton.Location = new System.Drawing.Point(12, 222);
            this.ceShowClearButton.Name = "ceShowClearButton";
            this.ceShowClearButton.Properties.Caption = "Show Clear Button";
            this.ceShowClearButton.Size = new System.Drawing.Size(261, 20);
            this.ceShowClearButton.StyleController = this.layoutControl;
            this.ceShowClearButton.TabIndex = 15;
            // 
            // ceShowFindButton
            // 
            this.ceShowFindButton.Location = new System.Drawing.Point(12, 246);
            this.ceShowFindButton.Name = "ceShowFindButton";
            this.ceShowFindButton.Properties.Caption = "Show Find Button";
            this.ceShowFindButton.Size = new System.Drawing.Size(261, 20);
            this.ceShowFindButton.StyleController = this.layoutControl;
            this.ceShowFindButton.TabIndex = 14;
            // 
            // ceAllowFindPanel
            // 
            this.ceAllowFindPanel.Location = new System.Drawing.Point(12, 150);
            this.ceAllowFindPanel.Name = "ceAllowFindPanel";
            this.ceAllowFindPanel.Properties.Caption = "Allow Find Panel";
            this.ceAllowFindPanel.Size = new System.Drawing.Size(261, 20);
            this.ceAllowFindPanel.StyleController = this.layoutControl;
            this.ceAllowFindPanel.TabIndex = 12;
            this.ceAllowFindPanel.CheckedChanged += new System.EventHandler(this.ceAllowFindPanel_CheckedChanged);
            // 
            // ceAlwaysVisible
            // 
            this.ceAlwaysVisible.Location = new System.Drawing.Point(12, 360);
            this.ceAlwaysVisible.Name = "ceAlwaysVisible";
            this.ceAlwaysVisible.Properties.Caption = "Always Visible";
            this.ceAlwaysVisible.Size = new System.Drawing.Size(261, 20);
            this.ceAlwaysVisible.StyleController = this.layoutControl;
            this.ceAlwaysVisible.TabIndex = 11;
            this.ceAlwaysVisible.CheckedChanged += new System.EventHandler(this.ceAlwaysVisible_CheckedChanged);
            // 
            // ceClearFindPanelOnClose
            // 
            this.ceClearFindPanelOnClose.Location = new System.Drawing.Point(12, 384);
            this.ceClearFindPanelOnClose.Name = "ceClearFindPanelOnClose";
            this.ceClearFindPanelOnClose.Properties.Caption = "Clear Find Panel on Close";
            this.ceClearFindPanelOnClose.Size = new System.Drawing.Size(261, 20);
            this.ceClearFindPanelOnClose.StyleController = this.layoutControl;
            this.ceClearFindPanelOnClose.TabIndex = 10;
            // 
            // ceHighlightFindResults
            // 
            this.ceHighlightFindResults.Location = new System.Drawing.Point(12, 174);
            this.ceHighlightFindResults.Name = "ceHighlightFindResults";
            this.ceHighlightFindResults.Properties.Caption = "Highlight Find Results";
            this.ceHighlightFindResults.Size = new System.Drawing.Size(261, 20);
            this.ceHighlightFindResults.StyleController = this.layoutControl;
            this.ceHighlightFindResults.TabIndex = 9;
            // 
            // seFindDelay
            // 
            this.seFindDelay.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.seFindDelay.Location = new System.Drawing.Point(100, 408);
            this.seFindDelay.Name = "seFindDelay";
            this.seFindDelay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seFindDelay.Properties.IsFloatValue = false;
            this.seFindDelay.Properties.Mask.EditMask = "N00";
            this.seFindDelay.Properties.MaxValue = new decimal(new int[] {
            5000,
            0,
            0,
            0});
            this.seFindDelay.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.seFindDelay.Size = new System.Drawing.Size(173, 20);
            this.seFindDelay.StyleController = this.layoutControl;
            this.seFindDelay.TabIndex = 4;
            this.seFindDelay.EditValueChanged += new System.EventHandler(this.seFindDelay_EditValueChanged);
            // 
            // cbFindMode
            // 
            this.cbFindMode.Location = new System.Drawing.Point(100, 456);
            this.cbFindMode.Name = "cbFindMode";
            this.cbFindMode.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbFindMode.Size = new System.Drawing.Size(173, 20);
            this.cbFindMode.StyleController = this.layoutControl;
            this.cbFindMode.TabIndex = 3;
            // 
            // cbFindFilterColumns
            // 
            this.cbFindFilterColumns.Location = new System.Drawing.Point(100, 432);
            this.cbFindFilterColumns.Name = "cbFindFilterColumns";
            this.cbFindFilterColumns.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbFindFilterColumns.Properties.DropDownRows = 25;
            this.cbFindFilterColumns.Size = new System.Drawing.Size(173, 20);
            this.cbFindFilterColumns.StyleController = this.layoutControl;
            this.cbFindFilterColumns.TabIndex = 2;
            this.cbFindFilterColumns.SelectedIndexChanged += new System.EventHandler(this.cbFindFilterColumns_SelectedIndexChanged);
            // 
            // imgFilterCondition
            // 
            this.imgFilterCondition.Location = new System.Drawing.Point(101, 83);
            this.imgFilterCondition.Name = "imgFilterCondition";
            this.imgFilterCondition.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.imgFilterCondition.Size = new System.Drawing.Size(171, 20);
            this.imgFilterCondition.StyleController = this.layoutControl;
            this.imgFilterCondition.TabIndex = 19;
            // 
            // layoutControlGroup
            // 
            this.layoutControlGroup.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.False;
            this.layoutControlGroup.GroupBordersVisible = false;
            this.layoutControlGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lgBehavior,
            this.customizationLayoutGroup,
            this.actionLayoutGroup,
            this.emptySpaceItem1});
            this.layoutControlGroup.Name = "Root";
            this.layoutControlGroup.Size = new System.Drawing.Size(285, 509);
            this.layoutControlGroup.TextVisible = false;
            // 
            // lgBehavior
            // 
            this.lgBehavior.GroupStyle = DevExpress.Utils.GroupStyle.Title;
            this.lgBehavior.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.behaviorLayoutItem,
            this.parserKindLayoutItem,
            this.filterConditionLayoutItem});
            this.lgBehavior.Location = new System.Drawing.Point(0, 0);
            this.lgBehavior.Name = "lgBehavior";
            this.lgBehavior.Size = new System.Drawing.Size(285, 116);
            this.lgBehavior.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.lgBehavior.Text = "Behavior";
            // 
            // behaviorLayoutItem
            // 
            this.behaviorLayoutItem.Control = this.imgBehavior;
            this.behaviorLayoutItem.Location = new System.Drawing.Point(0, 0);
            this.behaviorLayoutItem.Name = "behaviorLayoutItem";
            this.behaviorLayoutItem.Size = new System.Drawing.Size(265, 24);
            this.behaviorLayoutItem.Text = "Behavior:";
            this.behaviorLayoutItem.TextSize = new System.Drawing.Size(76, 13);
            // 
            // parserKindLayoutItem
            // 
            this.parserKindLayoutItem.Control = this.imgParserKind;
            this.parserKindLayoutItem.Location = new System.Drawing.Point(0, 24);
            this.parserKindLayoutItem.Name = "parserKindLayoutItem";
            this.parserKindLayoutItem.Size = new System.Drawing.Size(265, 24);
            this.parserKindLayoutItem.Text = "Parser Kind:";
            this.parserKindLayoutItem.TextSize = new System.Drawing.Size(76, 13);
            // 
            // filterConditionLayoutItem
            // 
            this.filterConditionLayoutItem.Control = this.imgFilterCondition;
            this.filterConditionLayoutItem.CustomizationFormText = "filterConditionLayoutItem";
            this.filterConditionLayoutItem.Location = new System.Drawing.Point(0, 48);
            this.filterConditionLayoutItem.Name = "filterConditionLayoutItem";
            this.filterConditionLayoutItem.Padding = new DevExpress.XtraLayout.Utils.Padding(3, 3, 3, 3);
            this.filterConditionLayoutItem.Size = new System.Drawing.Size(265, 26);
            this.filterConditionLayoutItem.Text = "Filter Condition:";
            this.filterConditionLayoutItem.TextSize = new System.Drawing.Size(76, 13);
            // 
            // customizationLayoutGroup
            // 
            this.customizationLayoutGroup.CustomizationFormText = "Customization";
            this.customizationLayoutGroup.GroupStyle = DevExpress.Utils.GroupStyle.Title;
            this.customizationLayoutGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.liShowCloseButton,
            this.liShowClearButton,
            this.liShowFindButton,
            this.showSearchNavButtonsLayoutItem,
            this.expandNodesOnSearchLayoutItem,
            this.liAllowFindPanel,
            this.liHighlightFindResults});
            this.customizationLayoutGroup.Location = new System.Drawing.Point(0, 116);
            this.customizationLayoutGroup.Name = "customizationLayoutGroup";
            this.customizationLayoutGroup.Size = new System.Drawing.Size(285, 210);
            this.customizationLayoutGroup.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.customizationLayoutGroup.Text = "Customization";
            // 
            // liShowCloseButton
            // 
            this.liShowCloseButton.Control = this.ceShowCloseButton;
            this.liShowCloseButton.Location = new System.Drawing.Point(0, 48);
            this.liShowCloseButton.Name = "liShowCloseButton";
            this.liShowCloseButton.Size = new System.Drawing.Size(265, 24);
            this.liShowCloseButton.TextSize = new System.Drawing.Size(0, 0);
            this.liShowCloseButton.TextVisible = false;
            // 
            // liShowClearButton
            // 
            this.liShowClearButton.Control = this.ceShowClearButton;
            this.liShowClearButton.Location = new System.Drawing.Point(0, 72);
            this.liShowClearButton.Name = "liShowClearButton";
            this.liShowClearButton.Size = new System.Drawing.Size(265, 24);
            this.liShowClearButton.TextSize = new System.Drawing.Size(0, 0);
            this.liShowClearButton.TextVisible = false;
            // 
            // liShowFindButton
            // 
            this.liShowFindButton.Control = this.ceShowFindButton;
            this.liShowFindButton.Location = new System.Drawing.Point(0, 96);
            this.liShowFindButton.Name = "liShowFindButton";
            this.liShowFindButton.Size = new System.Drawing.Size(265, 24);
            this.liShowFindButton.TextSize = new System.Drawing.Size(0, 0);
            this.liShowFindButton.TextVisible = false;
            // 
            // showSearchNavButtonsLayoutItem
            // 
            this.showSearchNavButtonsLayoutItem.Control = this.ceShowSearchNavButtons;
            this.showSearchNavButtonsLayoutItem.Location = new System.Drawing.Point(0, 120);
            this.showSearchNavButtonsLayoutItem.Name = "showSearchNavButtonsLayoutItem";
            this.showSearchNavButtonsLayoutItem.Size = new System.Drawing.Size(265, 24);
            this.showSearchNavButtonsLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.showSearchNavButtonsLayoutItem.TextVisible = false;
            // 
            // expandNodesOnSearchLayoutItem
            // 
            this.expandNodesOnSearchLayoutItem.Control = this.ceExpandNodesOnSearch;
            this.expandNodesOnSearchLayoutItem.Location = new System.Drawing.Point(0, 144);
            this.expandNodesOnSearchLayoutItem.Name = "expandNodesOnSearchLayoutItem";
            this.expandNodesOnSearchLayoutItem.Size = new System.Drawing.Size(265, 24);
            this.expandNodesOnSearchLayoutItem.TextSize = new System.Drawing.Size(0, 0);
            this.expandNodesOnSearchLayoutItem.TextVisible = false;
            // 
            // liAllowFindPanel
            // 
            this.liAllowFindPanel.Control = this.ceAllowFindPanel;
            this.liAllowFindPanel.Location = new System.Drawing.Point(0, 0);
            this.liAllowFindPanel.Name = "liAllowFindPanel";
            this.liAllowFindPanel.Size = new System.Drawing.Size(265, 24);
            this.liAllowFindPanel.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowFindPanel.TextVisible = false;
            // 
            // liHighlightFindResults
            // 
            this.liHighlightFindResults.Control = this.ceHighlightFindResults;
            this.liHighlightFindResults.Location = new System.Drawing.Point(0, 24);
            this.liHighlightFindResults.Name = "liHighlightFindResults";
            this.liHighlightFindResults.Size = new System.Drawing.Size(265, 24);
            this.liHighlightFindResults.TextSize = new System.Drawing.Size(0, 0);
            this.liHighlightFindResults.TextVisible = false;
            // 
            // actionLayoutGroup
            // 
            this.actionLayoutGroup.CustomizationFormText = "Action";
            this.actionLayoutGroup.GroupStyle = DevExpress.Utils.GroupStyle.Title;
            this.actionLayoutGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.liFindDelay,
            this.liFindFilterColumns,
            this.liMode,
            this.liAlwaysVisible,
            this.liClearFindPanelOnClose});
            this.actionLayoutGroup.Location = new System.Drawing.Point(0, 326);
            this.actionLayoutGroup.Name = "actionLayoutGroup";
            this.actionLayoutGroup.Size = new System.Drawing.Size(285, 162);
            this.actionLayoutGroup.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.actionLayoutGroup.Text = "Action";
            // 
            // liFindDelay
            // 
            this.liFindDelay.Control = this.seFindDelay;
            this.liFindDelay.Location = new System.Drawing.Point(0, 48);
            this.liFindDelay.Name = "liFindDelay";
            this.liFindDelay.Size = new System.Drawing.Size(265, 24);
            this.liFindDelay.Text = "Find Delay:";
            this.liFindDelay.TextSize = new System.Drawing.Size(76, 13);
            // 
            // liFindFilterColumns
            // 
            this.liFindFilterColumns.Control = this.cbFindFilterColumns;
            this.liFindFilterColumns.Location = new System.Drawing.Point(0, 72);
            this.liFindFilterColumns.Name = "liFindFilterColumns";
            this.liFindFilterColumns.Size = new System.Drawing.Size(265, 24);
            this.liFindFilterColumns.Text = "Filter Columns:";
            this.liFindFilterColumns.TextSize = new System.Drawing.Size(76, 13);
            // 
            // liMode
            // 
            this.liMode.Control = this.cbFindMode;
            this.liMode.Location = new System.Drawing.Point(0, 96);
            this.liMode.Name = "liMode";
            this.liMode.Size = new System.Drawing.Size(265, 24);
            this.liMode.Text = "Mode:";
            this.liMode.TextSize = new System.Drawing.Size(76, 13);
            // 
            // liAlwaysVisible
            // 
            this.liAlwaysVisible.Control = this.ceAlwaysVisible;
            this.liAlwaysVisible.Location = new System.Drawing.Point(0, 0);
            this.liAlwaysVisible.Name = "liAlwaysVisible";
            this.liAlwaysVisible.Size = new System.Drawing.Size(265, 24);
            this.liAlwaysVisible.TextSize = new System.Drawing.Size(0, 0);
            this.liAlwaysVisible.TextVisible = false;
            // 
            // liClearFindPanelOnClose
            // 
            this.liClearFindPanelOnClose.Control = this.ceClearFindPanelOnClose;
            this.liClearFindPanelOnClose.Location = new System.Drawing.Point(0, 24);
            this.liClearFindPanelOnClose.Name = "liClearFindPanelOnClose";
            this.liClearFindPanelOnClose.Size = new System.Drawing.Size(265, 24);
            this.liClearFindPanelOnClose.TextSize = new System.Drawing.Size(0, 0);
            this.liClearFindPanelOnClose.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 488);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(285, 21);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // ucFindOptions
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.layoutControl);
            this.Name = "ucFindOptions";
            this.Size = new System.Drawing.Size(285, 509);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl)).EndInit();
            this.layoutControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.imgParserKind.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowSearchNavButtons.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceExpandNodesOnSearch.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imgBehavior.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowCloseButton.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowClearButton.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowFindButton.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowFindPanel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAlwaysVisible.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceClearFindPanelOnClose.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceHighlightFindResults.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seFindDelay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFindMode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFindFilterColumns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imgFilterCondition.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgBehavior)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.parserKindLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.filterConditionLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.customizationLayoutGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowCloseButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowClearButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowFindButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.showSearchNavButtonsLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.expandNodesOnSearchLayoutItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowFindPanel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liHighlightFindResults)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.actionLayoutGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liFindDelay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liFindFilterColumns)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liMode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAlwaysVisible)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liClearFindPanelOnClose)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private XtraLayout.LayoutControl layoutControl;
        private XtraEditors.SpinEdit seFindDelay;
        private XtraEditors.ImageComboBoxEdit cbFindMode;
        private XtraEditors.ImageComboBoxEdit cbFindFilterColumns;
        private XtraLayout.LayoutControlGroup layoutControlGroup;
        private XtraLayout.LayoutControlItem liFindFilterColumns;
        private XtraLayout.LayoutControlItem liMode;
        private XtraLayout.LayoutControlItem liFindDelay;
        private XtraLayout.EmptySpaceItem emptySpaceItem1;
        private XtraLayout.LayoutControlGroup lgBehavior;
        private XtraEditors.CheckEdit ceAllowFindPanel;
        private XtraEditors.CheckEdit ceAlwaysVisible;
        private XtraEditors.CheckEdit ceClearFindPanelOnClose;
        private XtraEditors.CheckEdit ceHighlightFindResults;
        private XtraLayout.LayoutControlItem liAllowFindPanel;
        private XtraLayout.LayoutControlItem liAlwaysVisible;
        private XtraLayout.LayoutControlItem liClearFindPanelOnClose;
        private XtraLayout.LayoutControlItem liHighlightFindResults;
        private XtraLayout.LayoutControlGroup customizationLayoutGroup;
        private XtraEditors.CheckEdit ceShowCloseButton;
        private XtraEditors.CheckEdit ceShowClearButton;
        private XtraEditors.CheckEdit ceShowFindButton;
        private XtraLayout.LayoutControlItem liShowCloseButton;
        private XtraLayout.LayoutControlItem liShowClearButton;
        private XtraLayout.LayoutControlItem liShowFindButton;
        private XtraLayout.LayoutControlGroup actionLayoutGroup;
        private XtraEditors.ImageComboBoxEdit imgBehavior;
        private XtraLayout.LayoutControlItem behaviorLayoutItem;
        private XtraEditors.CheckEdit ceShowSearchNavButtons;
        private XtraLayout.LayoutControlItem showSearchNavButtonsLayoutItem;
        private XtraEditors.CheckEdit ceExpandNodesOnSearch;
        private XtraLayout.LayoutControlItem expandNodesOnSearchLayoutItem; 
        private XtraEditors.ImageComboBoxEdit imgParserKind;
        private XtraLayout.LayoutControlItem parserKindLayoutItem;
        private XtraEditors.ImageComboBoxEdit imgFilterCondition;
        private XtraLayout.LayoutControlItem filterConditionLayoutItem;
    }
}
