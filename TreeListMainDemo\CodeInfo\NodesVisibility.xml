<?xml version="1.0" encoding="UTF-8"?>
<totalinfo>
  <controls>
    <controlentry>
      <control>
        <name>treeList1</name>
        <windowcaption>Node visibility</windowcaption>
        <description>A node is hidden via the Visible property on a double-click.</description>
        <memberlist>TreeListNode.Visible</memberlist>
        <dtimage/>
      </control>
    </controlentry>
    <controlentry>
      <control>
        <name>listBoxControl1</name>
        <windowcaption>Restoring nodes</windowcaption>
        <description>Double-click an item to make a corresponding node visible.</description>
        <memberlist>TreeListNode.Visible</memberlist>
        <dtimage/>
      </control>
    </controlentry>
  </controls>
</totalinfo>