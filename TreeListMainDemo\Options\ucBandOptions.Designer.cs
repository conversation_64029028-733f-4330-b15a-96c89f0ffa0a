﻿namespace DevExpress.XtraTreeList.Demos.Options {
    partial class ucBandOptions {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing) {
            if(disposing && (components != null)) {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent() {
            this.layoutControl = new DevExpress.XtraLayout.LayoutControl();
            this.ceAllowBandColumnsMultiRow = new DevExpress.XtraEditors.CheckEdit();
            this.ceShowBands = new DevExpress.XtraEditors.CheckEdit();
            this.ceCustomizationFormSearchBoxVisible = new DevExpress.XtraEditors.CheckEdit();
            this.ceShowBandsInCustomizationForm = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowColumnResizing = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowColumnMoving = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowChangeColumnParent = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowChangeBandParent = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowBandResizing = new DevExpress.XtraEditors.CheckEdit();
            this.ceAllowBandMoving = new DevExpress.XtraEditors.CheckEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lgCustomization = new DevExpress.XtraLayout.LayoutControlGroup();
            this.liAllowBandMoving = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowBandResizing = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowChangeBandParent = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowChangeColumnParent = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowColumnMoving = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowColumnResizing = new DevExpress.XtraLayout.LayoutControlItem();
            this.liShowBandsInCustomizationForm = new DevExpress.XtraLayout.LayoutControlItem();
            this.liCustomizationFormSearchBoxVisible = new DevExpress.XtraLayout.LayoutControlItem();
            this.lgView = new DevExpress.XtraLayout.LayoutControlGroup();
            this.liShowBands = new DevExpress.XtraLayout.LayoutControlItem();
            this.liAllowBandColumnsMultiRow = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl)).BeginInit();
            this.layoutControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandColumnsMultiRow.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowBands.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceCustomizationFormSearchBoxVisible.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowBandsInCustomizationForm.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowColumnResizing.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowColumnMoving.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowChangeColumnParent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowChangeBandParent.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandResizing.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandMoving.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgCustomization)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandMoving)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandResizing)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowChangeBandParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowChangeColumnParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowColumnMoving)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowColumnResizing)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowBandsInCustomizationForm)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liCustomizationFormSearchBoxVisible)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowBands)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandColumnsMultiRow)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl
            // 
            this.layoutControl.Controls.Add(this.ceAllowBandColumnsMultiRow);
            this.layoutControl.Controls.Add(this.ceShowBands);
            this.layoutControl.Controls.Add(this.ceCustomizationFormSearchBoxVisible);
            this.layoutControl.Controls.Add(this.ceShowBandsInCustomizationForm);
            this.layoutControl.Controls.Add(this.ceAllowColumnResizing);
            this.layoutControl.Controls.Add(this.ceAllowColumnMoving);
            this.layoutControl.Controls.Add(this.ceAllowChangeColumnParent);
            this.layoutControl.Controls.Add(this.ceAllowChangeBandParent);
            this.layoutControl.Controls.Add(this.ceAllowBandResizing);
            this.layoutControl.Controls.Add(this.ceAllowBandMoving);
            this.layoutControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl.Location = new System.Drawing.Point(0, 0);
            this.layoutControl.Name = "layoutControl";
            this.layoutControl.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(701, 0, 650, 400);
            this.layoutControl.Root = this.Root;
            this.layoutControl.Size = new System.Drawing.Size(250, 340);
            this.layoutControl.TabIndex = 0;
            this.layoutControl.Text = "layoutControl1";
            // 
            // ceAllowBandColumnsMultiRow
            // 
            this.ceAllowBandColumnsMultiRow.Location = new System.Drawing.Point(12, 54);
            this.ceAllowBandColumnsMultiRow.Name = "ceAllowBandColumnsMultiRow";
            this.ceAllowBandColumnsMultiRow.Properties.Caption = "Allow Band Columns Multi Row";
            this.ceAllowBandColumnsMultiRow.Size = new System.Drawing.Size(226, 19);
            this.ceAllowBandColumnsMultiRow.StyleController = this.layoutControl;
            this.ceAllowBandColumnsMultiRow.TabIndex = 14;
            this.ceAllowBandColumnsMultiRow.CheckedChanged += new System.EventHandler(this.ceAllowBandColumnsMultiRow_CheckedChanged);
            // 
            // ceShowBands
            // 
            this.ceShowBands.Location = new System.Drawing.Point(12, 31);
            this.ceShowBands.Name = "ceShowBands";
            this.ceShowBands.Properties.Caption = "Show Bands";
            this.ceShowBands.Size = new System.Drawing.Size(226, 19);
            this.ceShowBands.StyleController = this.layoutControl;
            this.ceShowBands.TabIndex = 13;
            this.ceShowBands.CheckedChanged += new System.EventHandler(this.ceShowBands_CheckedChanged);
            // 
            // ceCustomizationFormSearchBoxVisible
            // 
            this.ceCustomizationFormSearchBoxVisible.Location = new System.Drawing.Point(12, 277);
            this.ceCustomizationFormSearchBoxVisible.Name = "ceCustomizationFormSearchBoxVisible";
            this.ceCustomizationFormSearchBoxVisible.Properties.Caption = "Customization Form Search Box Visible";
            this.ceCustomizationFormSearchBoxVisible.Size = new System.Drawing.Size(226, 19);
            this.ceCustomizationFormSearchBoxVisible.StyleController = this.layoutControl;
            this.ceCustomizationFormSearchBoxVisible.TabIndex = 12;
            this.ceCustomizationFormSearchBoxVisible.CheckedChanged += new System.EventHandler(this.ceCustomizationFormSearchBoxVisible_CheckedChanged);
            // 
            // ceShowBandsInCustomizationForm
            // 
            this.ceShowBandsInCustomizationForm.Location = new System.Drawing.Point(12, 254);
            this.ceShowBandsInCustomizationForm.Name = "ceShowBandsInCustomizationForm";
            this.ceShowBandsInCustomizationForm.Properties.Caption = "Show Bands In Customization Form";
            this.ceShowBandsInCustomizationForm.Size = new System.Drawing.Size(226, 19);
            this.ceShowBandsInCustomizationForm.StyleController = this.layoutControl;
            this.ceShowBandsInCustomizationForm.TabIndex = 11;
            this.ceShowBandsInCustomizationForm.CheckedChanged += new System.EventHandler(this.ceShowBandsInCustomizationForm_CheckedChanged);
            // 
            // ceAllowColumnResizing
            // 
            this.ceAllowColumnResizing.Location = new System.Drawing.Point(12, 231);
            this.ceAllowColumnResizing.Name = "ceAllowColumnResizing";
            this.ceAllowColumnResizing.Properties.Caption = "Allow Column Resizing";
            this.ceAllowColumnResizing.Size = new System.Drawing.Size(226, 19);
            this.ceAllowColumnResizing.StyleController = this.layoutControl;
            this.ceAllowColumnResizing.TabIndex = 10;
            this.ceAllowColumnResizing.CheckedChanged += new System.EventHandler(this.ceAllowColumnResizing_CheckedChanged);
            // 
            // ceAllowColumnMoving
            // 
            this.ceAllowColumnMoving.Location = new System.Drawing.Point(12, 208);
            this.ceAllowColumnMoving.Name = "ceAllowColumnMoving";
            this.ceAllowColumnMoving.Properties.Caption = "Allow Column Moving";
            this.ceAllowColumnMoving.Size = new System.Drawing.Size(226, 19);
            this.ceAllowColumnMoving.StyleController = this.layoutControl;
            this.ceAllowColumnMoving.TabIndex = 6;
            this.ceAllowColumnMoving.CheckedChanged += new System.EventHandler(this.ceAllowColumnMoving_CheckedChanged);
            // 
            // ceAllowChangeColumnParent
            // 
            this.ceAllowChangeColumnParent.Location = new System.Drawing.Point(12, 185);
            this.ceAllowChangeColumnParent.Name = "ceAllowChangeColumnParent";
            this.ceAllowChangeColumnParent.Properties.Caption = "Allow Change Column Parent";
            this.ceAllowChangeColumnParent.Size = new System.Drawing.Size(226, 19);
            this.ceAllowChangeColumnParent.StyleController = this.layoutControl;
            this.ceAllowChangeColumnParent.TabIndex = 9;
            this.ceAllowChangeColumnParent.CheckedChanged += new System.EventHandler(this.ceAllowChangeColumnParent_CheckedChanged);
            // 
            // ceAllowChangeBandParent
            // 
            this.ceAllowChangeBandParent.Location = new System.Drawing.Point(12, 162);
            this.ceAllowChangeBandParent.Name = "ceAllowChangeBandParent";
            this.ceAllowChangeBandParent.Properties.Caption = "Allow Change Band Parent";
            this.ceAllowChangeBandParent.Size = new System.Drawing.Size(226, 19);
            this.ceAllowChangeBandParent.StyleController = this.layoutControl;
            this.ceAllowChangeBandParent.TabIndex = 8;
            this.ceAllowChangeBandParent.CheckedChanged += new System.EventHandler(this.ceAllowChangeBandParent_CheckedChanged);
            // 
            // ceAllowBandResizing
            // 
            this.ceAllowBandResizing.Location = new System.Drawing.Point(12, 139);
            this.ceAllowBandResizing.Name = "ceAllowBandResizing";
            this.ceAllowBandResizing.Properties.Caption = "Allow Band Resizing";
            this.ceAllowBandResizing.Size = new System.Drawing.Size(226, 19);
            this.ceAllowBandResizing.StyleController = this.layoutControl;
            this.ceAllowBandResizing.TabIndex = 7;
            this.ceAllowBandResizing.CheckedChanged += new System.EventHandler(this.ceAllowBandResizing_CheckedChanged);
            // 
            // ceAllowBandMoving
            // 
            this.ceAllowBandMoving.Location = new System.Drawing.Point(12, 116);
            this.ceAllowBandMoving.Name = "ceAllowBandMoving";
            this.ceAllowBandMoving.Properties.Caption = "Allow Band Moving";
            this.ceAllowBandMoving.Size = new System.Drawing.Size(226, 19);
            this.ceAllowBandMoving.StyleController = this.layoutControl;
            this.ceAllowBandMoving.TabIndex = 6;
            this.ceAllowBandMoving.CheckedChanged += new System.EventHandler(this.ceAllowBandMoving_CheckedChanged);
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.False;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem1,
            this.lgCustomization,
            this.lgView});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(250, 340);
            this.Root.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 308);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(250, 32);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lgCustomization
            // 
            this.lgCustomization.GroupStyle = DevExpress.Utils.GroupStyle.Title;
            this.lgCustomization.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.liAllowBandMoving,
            this.liAllowBandResizing,
            this.liAllowChangeBandParent,
            this.liAllowChangeColumnParent,
            this.liAllowColumnMoving,
            this.liAllowColumnResizing,
            this.liShowBandsInCustomizationForm,
            this.liCustomizationFormSearchBoxVisible});
            this.lgCustomization.Location = new System.Drawing.Point(0, 85);
            this.lgCustomization.Name = "lgCustomization";
            this.lgCustomization.Size = new System.Drawing.Size(250, 223);
            this.lgCustomization.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.lgCustomization.Text = "Customization";
            // 
            // liAllowBandMoving
            // 
            this.liAllowBandMoving.Control = this.ceAllowBandMoving;
            this.liAllowBandMoving.Location = new System.Drawing.Point(0, 0);
            this.liAllowBandMoving.Name = "liAllowBandMoving";
            this.liAllowBandMoving.Size = new System.Drawing.Size(230, 23);
            this.liAllowBandMoving.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowBandMoving.TextVisible = false;
            // 
            // liAllowBandResizing
            // 
            this.liAllowBandResizing.Control = this.ceAllowBandResizing;
            this.liAllowBandResizing.Location = new System.Drawing.Point(0, 23);
            this.liAllowBandResizing.Name = "liAllowBandResizing";
            this.liAllowBandResizing.Size = new System.Drawing.Size(230, 23);
            this.liAllowBandResizing.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowBandResizing.TextVisible = false;
            // 
            // liAllowChangeBandParent
            // 
            this.liAllowChangeBandParent.Control = this.ceAllowChangeBandParent;
            this.liAllowChangeBandParent.Location = new System.Drawing.Point(0, 46);
            this.liAllowChangeBandParent.Name = "liAllowChangeBandParent";
            this.liAllowChangeBandParent.Size = new System.Drawing.Size(230, 23);
            this.liAllowChangeBandParent.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowChangeBandParent.TextVisible = false;
            // 
            // liAllowChangeColumnParent
            // 
            this.liAllowChangeColumnParent.Control = this.ceAllowChangeColumnParent;
            this.liAllowChangeColumnParent.Location = new System.Drawing.Point(0, 69);
            this.liAllowChangeColumnParent.Name = "liAllowChangeColumnParent";
            this.liAllowChangeColumnParent.Size = new System.Drawing.Size(230, 23);
            this.liAllowChangeColumnParent.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowChangeColumnParent.TextVisible = false;
            // 
            // liAllowColumnMoving
            // 
            this.liAllowColumnMoving.Control = this.ceAllowColumnMoving;
            this.liAllowColumnMoving.Location = new System.Drawing.Point(0, 92);
            this.liAllowColumnMoving.Name = "liAllowColumnMoving";
            this.liAllowColumnMoving.Size = new System.Drawing.Size(230, 23);
            this.liAllowColumnMoving.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowColumnMoving.TextVisible = false;
            // 
            // liAllowColumnResizing
            // 
            this.liAllowColumnResizing.Control = this.ceAllowColumnResizing;
            this.liAllowColumnResizing.Location = new System.Drawing.Point(0, 115);
            this.liAllowColumnResizing.Name = "liAllowColumnResizing";
            this.liAllowColumnResizing.Size = new System.Drawing.Size(230, 23);
            this.liAllowColumnResizing.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowColumnResizing.TextVisible = false;
            // 
            // liShowBandsInCustomizationForm
            // 
            this.liShowBandsInCustomizationForm.Control = this.ceShowBandsInCustomizationForm;
            this.liShowBandsInCustomizationForm.Location = new System.Drawing.Point(0, 138);
            this.liShowBandsInCustomizationForm.Name = "liShowBandsInCustomizationForm";
            this.liShowBandsInCustomizationForm.Size = new System.Drawing.Size(230, 23);
            this.liShowBandsInCustomizationForm.TextSize = new System.Drawing.Size(0, 0);
            this.liShowBandsInCustomizationForm.TextVisible = false;
            // 
            // liCustomizationFormSearchBoxVisible
            // 
            this.liCustomizationFormSearchBoxVisible.Control = this.ceCustomizationFormSearchBoxVisible;
            this.liCustomizationFormSearchBoxVisible.Location = new System.Drawing.Point(0, 161);
            this.liCustomizationFormSearchBoxVisible.Name = "liCustomizationFormSearchBoxVisible";
            this.liCustomizationFormSearchBoxVisible.Size = new System.Drawing.Size(230, 23);
            this.liCustomizationFormSearchBoxVisible.TextSize = new System.Drawing.Size(0, 0);
            this.liCustomizationFormSearchBoxVisible.TextVisible = false;
            // 
            // lgView
            // 
            this.lgView.CustomizationFormText = "View";
            this.lgView.GroupStyle = DevExpress.Utils.GroupStyle.Title;
            this.lgView.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.liShowBands,
            this.liAllowBandColumnsMultiRow});
            this.lgView.Location = new System.Drawing.Point(0, 0);
            this.lgView.Name = "lgView";
            this.lgView.Size = new System.Drawing.Size(250, 85);
            this.lgView.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.lgView.Text = "View";
            // 
            // liShowBands
            // 
            this.liShowBands.Control = this.ceShowBands;
            this.liShowBands.Location = new System.Drawing.Point(0, 0);
            this.liShowBands.Name = "liShowBands";
            this.liShowBands.Size = new System.Drawing.Size(230, 23);
            this.liShowBands.TextSize = new System.Drawing.Size(0, 0);
            this.liShowBands.TextVisible = false;
            // 
            // liAllowBandColumnsMultiRow
            // 
            this.liAllowBandColumnsMultiRow.Control = this.ceAllowBandColumnsMultiRow;
            this.liAllowBandColumnsMultiRow.Location = new System.Drawing.Point(0, 23);
            this.liAllowBandColumnsMultiRow.Name = "liAllowBandColumnsMultiRow";
            this.liAllowBandColumnsMultiRow.Size = new System.Drawing.Size(230, 23);
            this.liAllowBandColumnsMultiRow.TextSize = new System.Drawing.Size(0, 0);
            this.liAllowBandColumnsMultiRow.TextVisible = false;
            // 
            // ucBandOptions
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.layoutControl);
            this.Name = "ucBandOptions";
            this.Size = new System.Drawing.Size(250, 340);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl)).EndInit();
            this.layoutControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandColumnsMultiRow.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowBands.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceCustomizationFormSearchBoxVisible.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceShowBandsInCustomizationForm.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowColumnResizing.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowColumnMoving.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowChangeColumnParent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowChangeBandParent.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandResizing.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAllowBandMoving.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgCustomization)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandMoving)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandResizing)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowChangeBandParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowChangeColumnParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowColumnMoving)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowColumnResizing)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowBandsInCustomizationForm)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liCustomizationFormSearchBoxVisible)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lgView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liShowBands)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.liAllowBandColumnsMultiRow)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private XtraLayout.LayoutControl layoutControl;
        private XtraLayout.LayoutControlGroup Root;
        private XtraEditors.CheckEdit ceAllowChangeColumnParent;
        private XtraEditors.CheckEdit ceAllowChangeBandParent;
        private XtraEditors.CheckEdit ceAllowBandResizing;
        private XtraEditors.CheckEdit ceAllowBandMoving;
        private XtraLayout.LayoutControlItem liAllowBandMoving;
        private XtraLayout.EmptySpaceItem emptySpaceItem1;
        private XtraLayout.LayoutControlItem liAllowBandResizing;
        private XtraLayout.LayoutControlItem liAllowChangeBandParent;
        private XtraLayout.LayoutControlItem liAllowChangeColumnParent;
        private XtraEditors.CheckEdit ceAllowColumnMoving;
        private XtraLayout.LayoutControlItem liAllowColumnMoving;
        private XtraEditors.CheckEdit ceShowBandsInCustomizationForm;
        private XtraEditors.CheckEdit ceAllowColumnResizing;
        private XtraLayout.LayoutControlItem liAllowColumnResizing;
        private XtraLayout.LayoutControlItem liShowBandsInCustomizationForm;
        private XtraEditors.CheckEdit ceCustomizationFormSearchBoxVisible;
        private XtraLayout.LayoutControlItem liCustomizationFormSearchBoxVisible;
        private XtraEditors.CheckEdit ceShowBands;
        private XtraLayout.LayoutControlGroup lgCustomization;
        private XtraLayout.LayoutControlGroup lgView;
        private XtraLayout.LayoutControlItem liShowBands;
        private XtraEditors.CheckEdit ceAllowBandColumnsMultiRow;
        private XtraLayout.LayoutControlItem liAllowBandColumnsMultiRow;
    }
}
