﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList.Columns;

namespace DevExpress.XtraTreeList.Demos.Options {
    public partial class ucMultiSelectOptions : ucDefault {
        public ucMultiSelectOptions() {
            InitializeComponent();
        }
        protected override void InitDataCore() {
            //<optionsPage>
            ceAllowMultiSelect.Checked = TreeList.OptionsSelection.MultiSelect;
            imcMultiSelectMode.Properties.Items.AddEnum(typeof(TreeListMultiSelectMode));
            imcMultiSelectMode.EditValue = TreeList.OptionsSelection.MultiSelectMode;
            //</optionsPage>
            InitEnabled();
        }
        private void InitEnabled() {
            imcMultiSelectMode.Enabled = btnShowSelectedValues.Enabled = ceAllowMultiSelect.Checked;
        }
        private void cbMultiSelectMode_SelectedIndexChanged(object sender, EventArgs e) {
            TreeList.OptionsSelection.MultiSelectMode = (TreeListMultiSelectMode)imcMultiSelectMode.EditValue;
        }
        private void ceAllowMultiSelect_CheckedChanged(object sender, EventArgs e) {
            TreeList.OptionsSelection.MultiSelect = ceAllowMultiSelect.Checked;
            InitEnabled();
        }
        private void btnShowSelectedValues_Click(object sender, EventArgs e) {
            DemosHelper.ShowDescriptionForm(Control.MousePosition, new Size(400, 600), GetSelectionString(TreeList), TreeList.OptionsSelection.MultiSelectMode == TreeListMultiSelectMode.CellSelect ? "Selected Cells" : "Selected Rows");
        }
        string GetSelectionString(TreeList treeList) {
            string result = "";
            foreach(TreeListNode node in treeList.Selection) {
                if(result != "")
                    result += Environment.NewLine;
                result += string.Format("#{0}: {1} {2}", treeList.GetVisibleIndexByNode(node), node.GetDisplayText("FirstName"), node.GetDisplayText("LastName"));
                if(treeList.OptionsSelection.MultiSelectMode == TreeListMultiSelectMode.CellSelect) {
                    foreach(TreeListColumn cell in treeList.GetSelectedCells(node)) {
                        result += $"{Environment.NewLine}   {cell.FieldName}: {node.GetDisplayText(cell)}";
                    }
                }
            }
            return result;
        }
    }
}
